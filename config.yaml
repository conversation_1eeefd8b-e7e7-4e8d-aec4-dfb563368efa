# ================================================================================
# 一维振动信号数据增强项目配置文件
# 基于条件去噪扩散概率模型(CDDPM)和多尺度残差CNN的故障诊断

# ================================================================================
# 1. 数据集配置
# ================================================================================
dataset:
  name: "KAT"  # 数据集选择: "KAT", "SK", "JST" 或 ["KAT", "SK"] 多数据集对比实验
  datasets:
    KAT:
      num_classes: 8
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]
    SK:
      num_classes: 3
      class_names: ["0", "1", "2"]  # 正常 预警 故障
    JST:
      num_classes: 3
      class_names: ["0", "1", "2"]  # 正常 弱 强

  # ================================================================================
  # 2. 数据加载配置
  # ================================================================================
  data_loading:
    # 基础数据处理参数
    data_type: "sequential"           # 数据类型: "random" 或 "sequential"
    sample_selection: "sequential"    # 样本选择方式: "random" 或 "sequential"
    original_length: 1024             # 原始信号长度
    signal_length: 1024               # 截取后的信号长度
    train_val_split: 0.7              # 训练集在训练数据中的比例（剩余为验证集）
    normalize: true                   # 是否归一化
    normalization_method: "minmax"    # 归一化方法: minmax, zscore, robust

    # 故障样本配置
    fault_samples:
      max_fault_samples_per_class: [3]  # 故障样本数量: 单一值 或 [2, 3, 5] 多值对比实验

    # 健康样本处理配置
    healthy_samples:
      max_healthy_samples: -1         # 健康样本数量控制：
                                      #   -1: 与故障样本数量保持一致（动态匹配）
                                      #   0: 扩散训练中不使用健康样本
                                      #   >0: 使用指定数量的健康样本
      healthy_label: 0                # 健康样本的标签值（通常为0）

# ================================================================================
# 3. 数据增强配置
# ================================================================================
augmentation:
  method: "CDDPM"                     # 增强方法: "CDDPM", "CGAN", "WGAN", "WGAN-GP", "DDPM", "DCGAN", "CVAE"
                                      # 或 ["CDDPM", "CGAN"] 多方法对比实验
  num_generated_per_class: [3]        # 生成样本数量: 单一值 或 [5, 10, 15] 多值对比实验
  save_generated: true                # 是否保存生成的样本

  # 健康样本生成控制
  generate_fault_only: false           # 生成样本控制：
                                      #   true: 只生成故障样本，不生成健康样本
                                      #   false: 生成故障样本+健康样本（数量与num_generated_per_class一致）

  # 分类器训练的健康样本配置
  classifier_healthy_samples:
    use_real_when_no_generated: true  # 当没有生成健康样本时，是否使用真实健康样本
    real_healthy_count: -1            # 使用真实健康样本的数量：
                                      #   -1: 与故障样本数量保持一致
                                      #   0: 不使用健康样本（不推荐）
                                      #   >0: 使用指定数量

  # 生成策略配置（用于数据筛选功能）
  generation_strategy:
    target_samples_per_class: -1     # 每个类别的目标样本数：
                                      #   -1: 自动与故障样本数量保持一致（推荐）
                                      #   >0: 指定固定数量
                                      # 组合实验时会根据故障样本配置自动调整
    initial_multiplier: 5.0           # 初始生成倍数（生成target * multiplier个样本用于筛选）
    min_multiplier: 2.0               # 最小生成倍数
    max_multiplier: 10.0              # 最大生成倍数
    adaptive_generation: true         # 是否启用自适应生成（根据筛选效果调整）

  # ================================================================================
  # 4. 增强方法参数配置（所有方法都支持健康样本处理）
  # ================================================================================
  # CDDPM参数（主要方法）
  cddpm:
    timesteps: 1000                     # 扩散步数
    beta_schedule: "linear"             # 噪声调度: linear, cosine
    beta_start: 0.0001                  # 起始beta值
    beta_end: 0.02                      # 结束beta值

    # Classifier-Free Guidance 参数（条件/无条件混合训练）
    unconditional_prob: 0.1             # 无条件训练概率（10%的样本进行无条件训练）
    guidance_scale: 1.0                 # 引导强度（1.0=标准条件生成，>1.0=增强条件控制）

  # CGAN参数
  cgan:
    latent_dim: 100                     # 潜在空间维度
    generator_lr: 0.0002                # 生成器学习率
    discriminator_lr: 0.0002            # 判别器学习率
    beta1: 0.5                          # Adam优化器beta1参数
    epochs: 100                         # 训练轮数

  # WGAN参数
  wgan:
    latent_dim: 100                     # 潜在空间维度
    generator_lr: 0.00005               # 生成器学习率
    critic_lr: 0.00005                  # 判别器学习率
    n_critic: 5                         # 判别器训练次数
    clip_value: 0.01                    # 权重裁剪值
    epochs: 100                         # 训练轮数

  # WGAN-GP参数
  wgan_gp:
    latent_dim: 100                     # 潜在空间维度
    generator_lr: 0.0001                # 生成器学习率
    critic_lr: 0.0001                   # 判别器学习率
    lambda_gp: 10                       # 梯度惩罚系数
    n_critic: 5                         # 判别器训练次数
    epochs: 100                         # 训练轮数

  # DDPM参数（原始DDPM，无条件）
  ddpm:
    timesteps: 1000                     # 扩散步数
    beta_schedule: "linear"             # 噪声调度
    learning_rate: 0.0001               # 学习率
    epochs: 100                         # 训练轮数

  # DCGAN参数
  dcgan:
    latent_dim: 100                     # 潜在空间维度
    generator_lr: 0.0002                # 生成器学习率
    discriminator_lr: 0.0002            # 判别器学习率
    epochs: 100                         # 训练轮数

  # CVAE参数（条件变分自编码器）
  cvae:
    latent_dim: 100                     # 潜在空间维度
    hidden_dim: 512                     # 隐藏层维度
    learning_rate: 0.0001               # 学习率
    epochs: 100                         # 训练轮数

  # 传统数据增强方法参数
  traditional:
    adasyn:
      sampling_strategy: "auto"         # 采样策略
      n_neighbors: 5                    # 邻居数量
    smoteenn:
      sampling_strategy: "auto"         # 采样策略
    kmeans_smote:
      sampling_strategy: "auto"         # 采样策略
      k_neighbors: 5                    # 邻居数量
    rwo_sampling:
      sampling_strategy: "auto"         # 采样策略

# ================================================================================
# 5. 模型配置
# ================================================================================
models:
  # 扩散模型配置（用于CDDPM和DDPM）
  diffusion:
    in_channels: 1                    # 输入通道数（1D信号）
    out_channels: 1                   # 输出通道数
    model_channels: 64                # 基础通道数
    num_res_blocks: 2                 # 残差块数量
    attention_resolutions: [16, 8]    # 注意力分辨率
    channel_mult: [1, 2, 4]           # 通道倍数
    dropout: 0.1                      # Dropout率
    use_scale_shift_norm: true        # 使用缩放偏移归一化

  # 多尺度残差CNN配置（分类器）
  mrcnn:
    input_channels: 1                 # 输入通道数
    num_classes: 8                    # 类别数（根据数据集自动调整）
    base_channels: 64                 # 基础通道数
    num_blocks: 4                     # 残差块数量
    dropout: 0.1                      # Dropout率
    use_attention: true               # 使用注意力机制

# ================================================================================
# 6. 训练配置
# ================================================================================
training:
  # 扩散模型训练参数（基于论文Table V设置）
  diffusion:
    epochs: 10000                       # 训练轮数（使用10000轮次）
    batch_size: 64                    # 批次大小（统一使用64）
    learning_rate: 0.0001            # 学习率（0.00001，即1e-5）
    weight_decay: 0.0001              # 权重衰减

    # 最佳模型判断配置
    best_model_criteria:
      metric: "weighted_loss"         # 判断指标: "train_loss", "val_loss", "weighted_loss"
      mode: "min"                     # 优化方向: "min" (越小越好) 或 "max" (越大越好)

      # 加权损失配置 (仅当metric为"weighted_loss"时生效)
      weighted_loss:
        train_weight: 0.7             # 训练损失权重
        val_weight: 0.3               # 验证损失权重
        # 最终损失 = train_weight * train_loss + val_weight * val_loss

    # 学习率调度器
    scheduler:
      type: "cosine"                  # 调度器类型: none, cosine, step, exponential
      T_max: 10000                    # cosine调度器的最大epoch（与epochs保持一致）
      eta_min: 0.00001               # cosine调度器的最小学习率（比学习率小一个数量级）
      step_size: 2000                 # step调度器的步长（epochs的1/5）
      gamma: 0.5                      # step调度器的衰减因子

    # 早停配置
    early_stopping:
      enabled: false                  # 是否启用早停
      patience: 1000                  # 耐心值
      min_delta: 0.0001                # 最小改进
      monitor: "weighted_loss"        # 监控指标: "train_loss", "val_loss", "weighted_loss"
      mode: "min"                     # 模式: "min" (越小越好) 或 "max" (越大越好)
      restore_best_weights: true      # 恢复最佳权重

      # 加权损失配置 (仅当monitor为"weighted_loss"时生效)
      weighted_loss:
        train_weight: 0.7             # 训练损失权重
        val_weight: 0.3               # 验证损失权重

  # 分类器训练参数
  classifier:
    epochs: 100                       # 训练轮数（论文中分类器使用50轮）
    batch_size: 64                    # 批次大小（论文中统一使用64）
    learning_rate: 0.0001             # 学习率（论文中分类器使用1e-4）
    weight_decay: 0.01                # 权重衰减

    # 最佳模型判断配置
    best_model_criteria:
      metric: "weighted_loss"              # 判断指标: "train_loss", "val_loss", "weighted_loss"
      mode: "min"                     # 优化方向: "min" (越小越好) 或 "max" (越大越好)

      # 加权损失配置 (仅当metric为"weighted_loss"时生效)
      weighted_loss:
        train_weight: 0.7             # 训练损失权重
        val_weight: 0.3               # 验证损失权重

    # 学习率调度器
    scheduler:
      type: "cosine"                  # 调度器类型
      T_max: 100                       # cosine调度器的最大epoch（与epochs保持一致）
      eta_min: 0.00001                # cosine调度器的最小学习率

    # 早停配置
    early_stopping:
      enabled: false                   # 是否启用早停
      patience: 30                    # 耐心值
      min_delta: 0.001                # 最小改进
      monitor: "weighted_loss"             # 监控指标: "train_loss", "val_loss", "weighted_loss"
      mode: "min"                     # 模式: "min" (越小越好) 或 "max" (越大越好)
      restore_best_weights: true      # 恢复最佳权重

      # 加权损失配置 (仅当monitor为"weighted_loss"时生效)
      weighted_loss:
        train_weight: 0.7             # 训练损失权重
        val_weight: 0.3               # 验证损失权重

# ================================================================================
# 7. 数据筛选配置（高级功能，可选）
# ================================================================================
data_screening:
  enabled: false                      # 是否启用数据筛选
  screening_level: "basic"            # 筛选档位: basic, advanced, comprehensive

  # 目标数量控制
  target_control:
    enabled: true                     # 是否启用目标数量控制
    strict_target: false              # 是否严格控制目标数量
    fallback_strategy: "relax_thresholds"  # 不足时的回退策略

  # 置信度过滤
  confidence_filter:
    enabled: true                     # 是否启用置信度过滤
    threshold: 0.3                    # 置信度阈值（初始值，会自适应调整）
    min_threshold: 0.1                # 最小阈值（自适应调整的下限）
    max_threshold: 0.8                # 最大阈值（自适应调整的上限）
    per_class: false                  # 是否按类别独立设置阈值
    adaptive: true                    # 是否启用自适应阈值调整

  # Influence评分过滤
  influence_filter:
    enabled: false                    # 是否启用Influence评分过滤
    method: "tracin"                  # 方法: tracin, influence_function
    ratio: 0.3                        # 剔除负分前30%
    adaptive: true                    # 是否启用自适应调整

  # 离群检测
  outlier_detection:
    enabled: false                    # 是否启用离群检测
    method: "lof"                     # 方法: lof, one_class_svm, mahalanobis
    k_neighbors: 5                    # LOF方法的邻居数
    contamination: 0.1                # 离群点比例

  # 多样性选择
  diversity_selection:
    enabled: false                    # 是否启用多样性选择
    method: "kmeans"                  # 方法: kmeans, random
    n_clusters: 10                    # 聚类数量

# ================================================================================
# 8. 评估配置
# ================================================================================
evaluation:
  metrics:
    classification: ["accuracy", "precision", "recall", "f1_score"]  # 分类评估指标
    generation: ["gan_train", "gan_test"]                            # 生成评估指标

  # GAN-train和GAN-test评估
  gan_evaluation:
    classifier_epochs: 100            # 用于GAN评估的分类器训练轮数
    batch_size: 64                    # GAN评估时的批次大小

  # 可视化配置
  visualization:
    save_confusion_matrix: true       # 保存混淆矩阵
    save_tsne: true                   # 保存t-SNE可视化
    tsne_perplexity: 30               # t-SNE困惑度参数
    tsne_n_iter: 1000                 # t-SNE迭代次数
    save_sample_plots: true           # 保存样本图像
    num_samples_to_plot: 10           # 绘制的样本数量

# ================================================================================
# 9. 系统配置
# ================================================================================
system:
  device: "auto"                      # 设备选择: auto, cpu, cuda
  performance_mode: "high_performance"            # 性能模式: auto, fast, standard, high_performance, ultra
  seed: 42                            # 随机种子
  num_workers: "auto"                 # 数据加载器工作进程数：auto=自动判断，Windows=0，Linux=8
  pin_memory: false                   # 是否固定内存

  # 性能优化配置
  optimization:
    use_amp: false                    # 自动混合精度（auto模式会根据GPU自动启用）
    compile_model: false              # PyTorch 2.0编译优化（auto模式会自动启用）
    channels_last: false              # channels_last内存格式（auto模式会自动启用）
    benchmark: true                   # cuDNN benchmark（auto模式会自动启用）

  # 保存配置
  save:
    results_dir: "results"            # 结果保存目录
    checkpoints_dir: "checkpoints"    # 检查点保存目录
    logs_dir: "logs"                  # 日志保存目录
    generated_samples_dir: "generated_samples/{dataset_name}"  # 生成样本保存目录
    combined_datasets_dir: "results/combined_datasets"        # 组合数据集保存目录

    # 检查点保存策略
    save_best_only: true              # 是否只保存最佳模型（true=只保存最佳，false=保存过程+最佳）
    save_every_n_epochs: 1000         # 过程检查点保存间隔（仅当save_best_only=false时生效）
    max_checkpoints_to_keep: 3        # 最多保留多少个过程检查点（仅当save_best_only=false时生效）

# ================================================================================
# 10. 实验配置
# ================================================================================
experiment:
  name: "cddpm_fault_diagnosis"       # 实验名称
  description: "基于CDDPM的一维振动信号故障诊断数据增强"
  tags: ["CDDPM", "fault_diagnosis", "vibration_signal"]

  # 结果保存配置
  results:
    save_individual: true             # 保存单次实验结果
    save_comparison_csv: true         # 保存对比实验汇总CSV
    save_plots_csv: true              # 保存绘图数据的CSV文件
    create_timestamp_folder: true     # 创建时间戳文件夹

# ================================================================================
# 11. 性能模式配置（固定配置，不常修改）
# ================================================================================
performance_profiles:
  # 标准模式 - 默认模式，只调整批次大小，epochs使用原始配置
  standard:
    training:
      diffusion:
        batch_size: 64                # 论文中统一使用64
        # epochs使用原始training.diffusion.epochs配置
      classifier:
        batch_size: 64                # 论文中统一使用64
        # epochs使用原始training.classifier.epochs配置
    system:
      num_workers: "auto"             # 自动根据系统类型判断：Windows=0，Linux=8
      pin_memory: false               # 不启用内存固定
      optimization:
        use_amp: false                # 不使用混合精度
        compile_model: false          # 不使用模型编译
        channels_last: false          # 不使用channels_last
        benchmark: true               # 启用cuDNN benchmark

  # 快速模式 - 适用于快速测试和调试，减少批次大小和轮数
  fast:
    training:
      diffusion:
        batch_size: 32                # 较小批次，快速测试
        epochs: 2                    # 减少轮数用于快速测试
      classifier:
        batch_size: 32                # 较小批次，快速测试
        epochs: 2                    # 减少轮数用于快速测试
    system:
      num_workers: "auto"             # 自动根据系统类型判断
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true

  # 高性能模式 - 适用于高端GPU，增大批次大小，epochs使用原始配置
  high_performance:
    training:
      diffusion:
        batch_size: 128               # 大批次，充分利用GPU
        # epochs使用原始training.diffusion.epochs配置
      classifier:
        batch_size: 128               # 大批次，充分利用GPU
        # epochs使用原始training.classifier.epochs配置
    system:
      num_workers: "auto"             # 自动根据系统类型判断：Windows=0，Linux=16
      pin_memory: true                # 启用内存固定
      optimization:
        use_amp: true                 # 启用混合精度训练
        compile_model: true           # 启用PyTorch 2.0编译优化
        channels_last: true           # 启用channels_last内存格式
        benchmark: true               # 启用cuDNN benchmark

  # 超高性能模式 - 适用于顶级GPU，超大批次，epochs使用原始配置
  ultra:
    training:
      diffusion:
        batch_size: 256               # 超大批次，充分利用显存
        # epochs使用原始training.diffusion.epochs配置
      classifier:
        batch_size: 256               # 超大批次，充分利用显存
        # epochs使用原始training.classifier.epochs配置
    system:
      num_workers: "auto"             # 自动根据系统类型判断：Windows=0，Linux=8
      pin_memory: true                # 启用内存固定
      optimization:
        use_amp: true                 # 启用混合精度训练
        compile_model: true           # 启用PyTorch 2.0编译优化
        channels_last: true           # 启用channels_last内存格式
        benchmark: true               # 启用cuDNN benchmark
