% KAT数据集预处理程序 - 顺序采样版本（限制样本数量）
% 功能说明：
% 1. 处理KAT轴承故障数据集，采用顺序采样方式而非随机采样
% 2. 支持设置重叠长度，按顺序依次提取样本（如1-1024为第一个样本，513-1536为第二个样本等）
% 3. 每个类别只使用第一个mat文件进行处理，确保数据一致性
% 4. 训练集和测试集分别从不同的信号段提取，避免数据泄露
% 5. 可配置样本长度、重叠长度、生成样本数量等参数
% 6. 数据保存为MATLAB和NPY格式，便于后续使用
% 
% 此版本展示如何限制生成的样本数量
% 数据集包含8个类别（0-7），每个类别包含20个文件
% 每个文件中包含与文件名相同的结构体，振动信号存储在结构体的Y(7).Data中

% 清空工作区和命令窗口
clear;
clc;

try
    % 开始计时
    tic;
    
    % 调用处理函数
    process_KAT_dataset_sequential_limited();
    
    % 结束计时
    elapsed_time = toc;
    fprintf('处理完成! 耗时: %.2f 秒\n', elapsed_time);
catch e
    % 捕获并显示错误
    fprintf('处理过程中发生错误:\n');
    disp(e.message);
    if ~isempty(e.stack)
        fprintf('错误位置: %s (行 %d)\n', e.stack(1).name, e.stack(1).line);
    end
end

% ================== 主处理函数 ==================
function process_KAT_dataset_sequential_limited()
    % ==================== 参数设置 ====================
    sample_length = 1024;           % 每个样本的长度（采样点数）
    overlap_length = 512;           % 重叠长度（采样点数），控制相邻样本的重叠程度
    train_ratio = 0.7;              % 训练集从前70%长度选择，测试集从后30%长度选择
    use_first_file_only = true;     % 是否只使用每个类别的第一个文件
    random_seed = 42;               % 随机种子，确保结果可重现（用于数据打乱）
    
    % 样本数量控制参数（示例配置）
    max_samples_per_signal = 10;    % 每个信号最多提取10个样本
    train_samples_per_class = 200;  % 每个类别最多生成200个训练样本
    test_samples_per_class = 200;   % 每个类别最多生成200个测试样本
    
    % ==================== 路径设置 ====================
    % 获取脚本所在目录的上级目录作为基础路径
    script_dir = fileparts(mfilename('fullpath'));
    base_dir = fileparts(script_dir);  % 上级目录
    
    % 使用基础路径构建完整路径
    ori_path = fullfile(base_dir, 'dataset', 'KAT', 'ori_mat');      % 原始数据目录
    final_mat_path = fullfile(base_dir, 'dataset', 'KAT', 'used_mat_sequential_limited');  % 最终mat文件保存目录
    final_npy_path = fullfile(base_dir, 'dataset', 'KAT', 'used_npy_sequential_limited');  % 最终npy文件保存目录
    
    fprintf('使用以下路径:\n');
    fprintf('原始数据目录: %s\n', ori_path);
    fprintf('Mat文件保存目录: %s\n', final_mat_path);
    fprintf('NPY文件保存目录: %s\n', final_npy_path);
    fprintf('参数设置:\n');
    fprintf('- 样本长度: %d 个采样点\n', sample_length);
    fprintf('- 重叠长度: %d 个采样点\n', overlap_length);
    fprintf('- 步长: %d 个采样点\n', sample_length - overlap_length);
    fprintf('- 训练集从前%.0f%%选取，测试集从后%.0f%%选取\n', train_ratio*100, (1-train_ratio)*100);
    if use_first_file_only
        fprintf('- 采样策略: 每个类别只使用第一个文件\n');
    else
        fprintf('- 采样策略: 使用每个类别的所有文件\n');
    end
    fprintf('- 每个信号最多提取: %d 个样本\n', max_samples_per_signal);
    fprintf('- 每个类别最多训练样本: %d 个\n', train_samples_per_class);
    fprintf('- 每个类别最多测试样本: %d 个\n', test_samples_per_class);
    
    % 检查原始数据目录是否存在
    if ~exist(ori_path, 'dir')
        error('原始数据目录不存在: %s', ori_path);
    end
    
    % 检查参数合理性
    if overlap_length >= sample_length
        error('重叠长度(%d)不能大于等于样本长度(%d)', overlap_length, sample_length);
    end
    
    fprintf('开始处理KAT数据集...\n');
    
    % ==================== 创建保存目录 ====================
    if ~exist(fullfile(final_mat_path, 'train'), 'dir')
        mkdir(fullfile(final_mat_path, 'train'));
    end
    if ~exist(fullfile(final_mat_path, 'test'), 'dir')
        mkdir(fullfile(final_mat_path, 'test'));
    end
    if ~exist(fullfile(final_npy_path, 'train'), 'dir')
        mkdir(fullfile(final_npy_path, 'train'));
    end
    if ~exist(fullfile(final_npy_path, 'test'), 'dir')
        mkdir(fullfile(final_npy_path, 'test'));
    end
    
    % ==================== 读取原始数据 ====================
    fprintf('读取原始数据文件...\n');
    
    % 获取所有子目录（类别目录）
    class_dirs = dir(ori_path);
    class_dirs = class_dirs([class_dirs.isdir]);
    class_dirs = class_dirs(~ismember({class_dirs.name}, {'.', '..'}));
    
    % 设置随机种子以确保结果可重现
    rng(random_seed);
    
    % 准备存储训练和测试样本
    all_train_data = [];
    all_train_labels = [];
    all_test_data = [];
    all_test_labels = [];
    
    % 存储类别名称用于映射
    class_names = cell(length(class_dirs), 1);
    
    % 遍历所有类别目录
    for class_idx = 1:length(class_dirs)
        class_id = class_idx - 1;  % 类别ID从0开始
        class_dir_name = class_dirs(class_idx).name;
        class_names{class_idx} = class_dir_name;
        
        fprintf('处理类别 %d: %s\n', class_id, class_dir_name);
        
        % 获取当前类别目录下的所有.mat文件
        class_path = fullfile(ori_path, class_dir_name);
        mat_files = dir(fullfile(class_path, '*.mat'));
        
        % 确保至少有一个文件
        if isempty(mat_files)
            warning('类别 %d (%s) 目录下没有.mat文件，跳过处理', class_id, class_dir_name);
            continue;
        end
        
        % 根据设置决定处理哪些文件
        if use_first_file_only
            % 只处理第一个文件
            files_to_process = mat_files(1);
            fprintf('  只使用第一个文件: %s\n', files_to_process.name);
        else
            % 处理所有文件
            files_to_process = mat_files;
            fprintf('  使用所有 %d 个文件\n', length(files_to_process));
        end
        
        % 初始化当前类别的样本计数
        current_class_train_count = 0;
        current_class_test_count = 0;
        
        % 遍历要处理的文件
        for file_idx = 1:length(files_to_process)
            file_path = fullfile(class_path, files_to_process(file_idx).name);
            [~, file_name, ~] = fileparts(files_to_process(file_idx).name);
            
            fprintf('  处理文件: %s\n', file_name);
            
            try
                % 加载数据文件
                data = load(file_path);
                
                % 获取与文件名相同的结构体
                if ~isfield(data, file_name)
                    warning('文件 %s 不包含同名结构体，尝试查找其他结构体', file_path);
                    fields = fieldnames(data);
                    if isempty(fields)
                        warning('文件 %s 不包含任何数据，跳过处理', file_path);
                        continue;
                    end
                    field_name = fields{1};
                else
                    field_name = file_name;
                end
                
                % 检查并提取振动信号
                if ~isfield(data.(field_name), 'Y') || length(data.(field_name).Y) < 7 || ...
                   ~isfield(data.(field_name).Y(7), 'Data')
                    warning('文件 %s 结构不符合预期 (无法找到 Y(7).Data)，跳过处理', file_path);
                    continue;
                end
                
                % 获取振动信号
                signal = data.(field_name).Y(7).Data;
                
                % 确保信号是一维的
                if size(signal, 1) > size(signal, 2)
                    signal = signal';
                end
                
                % 确保信号长度足够
                if length(signal) < sample_length
                    warning('文件 %s 的信号长度不足 (%d < %d)，跳过处理', ...
                        file_path, length(signal), sample_length);
                    continue;
                end
                
                % 计算训练和测试区域的分割点
                split_point = floor(length(signal) * train_ratio);
                
                % 从信号的前train_ratio部分提取训练样本（顺序采样）
                train_signal = signal(1:split_point);
                train_samples = extract_sequential_samples_limited(train_signal, sample_length, overlap_length, max_samples_per_signal);
                
                % 检查训练样本数量限制
                if train_samples_per_class > 0 && current_class_train_count + size(train_samples, 1) > train_samples_per_class
                    remaining_train = train_samples_per_class - current_class_train_count;
                    if remaining_train > 0
                        train_samples = train_samples(1:remaining_train, :);
                    else
                        train_samples = [];
                    end
                end
                train_labels = ones(size(train_samples, 1), 1) * class_id;
                
                % 从信号的后(1-train_ratio)部分提取测试样本（顺序采样）
                test_signal = signal(split_point+1:end);
                test_samples = extract_sequential_samples_limited(test_signal, sample_length, overlap_length, max_samples_per_signal);
                
                % 检查测试样本数量限制
                if test_samples_per_class > 0 && current_class_test_count + size(test_samples, 1) > test_samples_per_class
                    remaining_test = test_samples_per_class - current_class_test_count;
                    if remaining_test > 0
                        test_samples = test_samples(1:remaining_test, :);
                    else
                        test_samples = [];
                    end
                end
                test_labels = ones(size(test_samples, 1), 1) * class_id;
                
                % 添加到总样本集
                all_train_data = [all_train_data; train_samples];
                all_train_labels = [all_train_labels; train_labels];
                all_test_data = [all_test_data; test_samples];
                all_test_labels = [all_test_labels; test_labels];
                
                % 更新当前类别的样本计数
                current_class_train_count = current_class_train_count + size(train_samples, 1);
                current_class_test_count = current_class_test_count + size(test_samples, 1);
                
                fprintf('    提取了 %d 个训练样本和 %d 个测试样本\n', ...
                    size(train_samples, 1), size(test_samples, 1));
                
                % 检查是否达到样本数量限制，如果达到则停止处理
                train_limit_reached = train_samples_per_class > 0 && current_class_train_count >= train_samples_per_class;
                test_limit_reached = test_samples_per_class > 0 && current_class_test_count >= test_samples_per_class;
                
                if train_limit_reached && test_limit_reached
                    fprintf('    已达到训练和测试样本数量限制，停止处理当前类别\n');
                    break;
                elseif train_limit_reached
                    fprintf('    已达到训练样本数量限制\n');
                elseif test_limit_reached
                    fprintf('    已达到测试样本数量限制\n');
                end
                
            catch e
                warning('处理文件 %s 时出错: %s', file_path, e.message);
                continue;
            end
        end
        
        fprintf('类别 %d: 总共生成了 %d 个训练样本和 %d 个测试样本\n', ...
            class_id, current_class_train_count, current_class_test_count);
    end
    
    % 随机打乱训练集和测试集
    train_shuffle_idx = randperm(size(all_train_data, 1));
    all_train_data = all_train_data(train_shuffle_idx, :);
    all_train_labels = int32(all_train_labels(train_shuffle_idx));  % 转换为int32类型
    
    test_shuffle_idx = randperm(size(all_test_data, 1));
    all_test_data = all_test_data(test_shuffle_idx, :);
    all_test_labels = int32(all_test_labels(test_shuffle_idx));  % 转换为int32类型
    
    fprintf('最终训练集: %d 个样本\n', size(all_train_data, 1));
    fprintf('最终测试集: %d 个样本\n', size(all_test_data, 1));
    
    % ==================== 保存为MATLAB格式 ====================
    fprintf('保存为MATLAB格式...\n');
    
    % 保存训练集
    save(fullfile(final_mat_path, 'train', 'train_dataset_sequential_limited.mat'), 'all_train_data', 'all_train_labels');
    
    % 保存测试集
    save(fullfile(final_mat_path, 'test', 'test_dataset_sequential_limited.mat'), 'all_test_data', 'all_test_labels');
    
    fprintf('KAT数据集预处理完成（顺序采样-限制数量版本）!\n');
end

function samples = extract_sequential_samples_limited(signal, sample_length, overlap_length, max_samples)
    % 从信号中按顺序提取固定长度的样本，支持重叠采样（限制数量版本）
    % 输入:
    %   signal: 原始信号
    %   sample_length: 每个样本的长度
    %   overlap_length: 相邻样本的重叠长度
    %   max_samples: 最大提取样本数量（如果为0或负数，则提取所有可能的样本）
    % 输出:
    %   samples: 提取的样本矩阵，每行一个样本

    signal_length = length(signal);
    step_size = sample_length - overlap_length;  % 步长

    % 检查信号长度是否足够
    if signal_length < sample_length
        error('信号长度(%d)不足以提取长度为%d的样本', signal_length, sample_length);
    end

    % 检查步长是否合理
    if step_size <= 0
        error('重叠长度(%d)不能大于等于样本长度(%d)', overlap_length, sample_length);
    end

    % 计算可以提取的最大样本数
    max_possible_samples = floor((signal_length - sample_length) / step_size) + 1;

    % 确定实际提取的样本数
    if max_samples <= 0 || max_samples > max_possible_samples
        num_samples = max_possible_samples;
    else
        num_samples = max_samples;
    end

    % 初始化样本矩阵
    samples = zeros(num_samples, sample_length);

    % 按顺序提取样本
    for i = 1:num_samples
        start_idx = (i - 1) * step_size + 1;
        end_idx = start_idx + sample_length - 1;
        samples(i, :) = signal(start_idx:end_idx);
    end

    fprintf('    从长度为%d的信号中提取了%d个样本（步长=%d，重叠=%d）\n', ...
        signal_length, num_samples, step_size, overlap_length);
end
