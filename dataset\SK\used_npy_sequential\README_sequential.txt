SK数据集 - NPY格式数据说明（顺序采样版本）
==========================================

数据格式说明:
- NPY文件格式: 
  * 训练数据: sk_data_sequential.npy (样本矩阵), sk_label_sequential.npy (整数类型标签)
  * 测试数据: sk_data_sequential.npy (样本矩阵), sk_label_sequential.npy (整数类型标签)

标签说明:
- 0: 正常
- 1: 故障预警
- 2: 故障

样本数量:
- 训练集: 240 个样本
- 测试集: 240 个样本

采样方法:
- 采用顺序采样方式，在每个4096点原始样本内按顺序提取1024点子样本
- 训练样本从每个类别的前50%样本选取，测试样本从后50%样本选取
- 每个子样本包含 1024 个数据点，相邻子样本重叠 0 个数据点
- 原始样本长度: 4096 个数据点
