KAT数据集 - NPY格式数据说明（顺序采样版本）
==========================================

数据格式说明:
- NPY文件格式: 
  * 训练数据: kat_data_sequential.npy (样本矩阵), kat_label_sequential.npy (整数类型标签)
  * 测试数据: kat_data_sequential.npy (样本矩阵), kat_label_sequential.npy (整数类型标签)

标签说明:
- 0: K001
- 1: KA01
- 2: KA05
- 3: KA09
- 4: KI01
- 5: KI03
- 6: KI05
- 7: KI08

样本数量:
- 训练集: 每个类别不限制数量 (实际生成 1001 个样本)
- 测试集: 每个类别不限制数量 (实际生成 1001 个样本)

采样方法:
- 采用顺序采样方式，按设定的重叠长度依次提取样本
- 每个类别只使用第一个文件进行采样
- 训练样本从信号的前50%长度选取，测试样本从后50%长度选取
- 每个样本包含 1024 个数据点，相邻样本重叠 0 个数据点
