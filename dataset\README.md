# 轴承故障诊断数据集

## 数据集结构

该数据集包含多个轴承故障诊断相关的数据集，具体包括：

- CWRU (凯斯西储大学轴承数据集)
- JST
- KAT
- MAFAULDA
- MFPT
- SK

### 文件组织结构

每个数据集的组织结构如下：

```
数据集名称/
├── ori_mat/             # 原始MATLAB格式数据
│   └── [分类文件夹]     # 按故障类型/工况等分类
├── used_mat/            # 处理后用于实验的MATLAB格式数据
│   ├── test/            # 测试数据
│   └── train/           # 训练数据
└── used_npy/            # 处理后用于实验的NumPy格式数据
    ├── test/            # 测试数据
    └── train/           # 训练数据
```

### 数据集详情

#### CWRU
CWRU数据集中的`ori_mat`文件夹下包含0,1,2,3四种不同类型的故障数据。

#### JST
JST数据集中的`ori_mat`文件夹下包含0,1,2三种不同类型的故障数据。

#### KAT
KAT数据集中的`ori_mat`文件夹下包含K001,KA01,KA05,KA09,KI01,KI03,KI05,KI08等不同类型的数据。

#### MAFAULDA
MAFAULDA数据集的`ori_mat`文件夹下包含多种工况数据：
- 1_normal：正常数据
- 2_imbalance：不平衡故障，包含20g、35g和6g三种不同程度
- 3_horizontal_misalignment：水平偏心，包含0.50和2.00两种不同程度
- 4_vertical_misalignment：垂直偏心，包含0.51、1.27和1.90三种不同程度
- 5_overhang：外悬故障，包含ball_fault_20g、cage_fault_20g和outer_race_20g
- 6_underhang：内悬故障，包含ball_fault_20g、cage_fault_20g和outer_race_20g

#### MFPT
MFPT数据集的`ori_mat`文件夹下包含：
- all：全部数据，包含0-8共9种类型
- test：测试数据，包含0-8共9种类型
- train：训练数据，包含0-8共9种类型

#### SK
SK数据集保持了与其他数据集相同的组织结构，但`ori_mat`中的具体分类不明显。

## 数据格式

- .mat文件：MATLAB格式的数据文件
- .npy文件：NumPy格式的数据文件，可通过Python的NumPy库直接读取

## used_npy 数据详细说明

### 数据结构

每个数据集的`used_npy`目录包含两类NumPy格式文件：
- `{数据集名称}_data.npy`：存储振动信号数据
- `{数据集名称}_label.npy`：存储对应的标签数据

例如，对于CWRU数据集，文件分别为`cwru_data.npy`和`cwru_label.npy`。

### 数据格式

1. **数据文件 (`*_data.npy`)**
   - 维度：(样本数, 信号长度)
   - 例如CWRU数据集中维度为(800, 1024)，表示800个样本，每个样本包含1024个数据点
   - 数据类型：float64
   - 内容：归一化处理后的振动信号时域数据

2. **标签文件 (`*_label.npy`)**
   - 维度：(样本数, 1)
   - 数据类型：uint8
   - 内容：整数标签，表示故障类别

### 重点数据集格式说明

#### KAT 数据集
- **数据文件 (kat_data.npy)**
  - 维度：(1600, 1024)
  - 数据类型：float64
  - 样本数量：1600个
  - 每个样本包含1024个数据点

- **标签文件 (kat_label.npy)**
  - 维度：(1600, 1)
  - 数据类型：int32
  - 标签类别：0-7，共8种故障类型
  - 标签数量分布：各类型样本数量需通过`np.unique(y, return_counts=True)`查看

#### SK 数据集
- **数据文件 (sk_data.npy)**
  - 维度：(336, 1024)
  - 数据类型：float64
  - 样本数量：336个
  - 每个样本包含1024个数据点

- **标签文件 (sk_label.npy)**
  - 维度：(336, 1)
  - 数据类型：int32
  - 标签类别：0-2，共3种故障类型
  - 标签数量分布：各类型样本数量需通过`np.unique(y, return_counts=True)`查看

#### JST 数据集
- **数据文件 (jst_data.npy)**
  - 维度：(600, 1024)
  - 数据类型：uint16（注意：与其他数据集不同，JST使用uint16类型）
  - 样本数量：600个
  - 每个样本包含1024个数据点

- **标签文件 (jst_label.npy)**
  - 维度：(600, 1)
  - 数据类型：int32
  - 标签类别：0-2，共3种故障类型
  - 标签数量分布：各类型样本数量需通过`np.unique(y, return_counts=True)`查看

### Python加载方式

可以使用以下Python代码加载数据：

```python
import numpy as np
import os

# 指定数据集和类型
dataset_name = 'CWRU'  # 可选: 'CWRU', 'JST', 'KAT', 'MAFAULDA', 'MFPT', 'SK'
data_type = 'train'    # 可选: 'train', 'test'

# 构建文件路径
data_path = os.path.join(dataset_name, 'used_npy', data_type, f'{dataset_name.lower()}_data.npy')
label_path = os.path.join(dataset_name, 'used_npy', data_type, f'{dataset_name.lower()}_label.npy')

# 加载数据
X = np.load(data_path)
y = np.load(label_path)

print(f"数据形状: {X.shape}")
print(f"标签形状: {y.shape}")
print(f"数据类型: {X.dtype}")
print(f"标签类型: {y.dtype}")
print(f"标签分布: {np.unique(y, return_counts=True)}")
```

### 多数据集加载示例

如需同时加载多个数据集进行训练或测试，可以使用以下代码：

```python
import numpy as np
import os

def load_datasets(dataset_names, data_type='train'):
    """
    加载多个数据集
    
    参数:
        dataset_names: 数据集名称列表，如 ['CWRU', 'MFPT']
        data_type: 'train' 或 'test'
        
    返回:
        X_all: 所有数据集合并后的数据
        y_all: 所有数据集合并后的标签
    """
    X_all = []
    y_all = []
    
    for dataset in dataset_names:
        # 构建文件路径
        data_path = os.path.join(dataset, 'used_npy', data_type, f'{dataset.lower()}_data.npy')
        label_path = os.path.join(dataset, 'used_npy', data_type, f'{dataset.lower()}_label.npy')
        
        # 加载数据
        if os.path.exists(data_path) and os.path.exists(label_path):
            X = np.load(data_path)
            y = np.load(label_path)
            
            X_all.append(X)
            y_all.append(y)
            print(f"已加载 {dataset} 数据集: {X.shape} 样本")
        else:
            print(f"警告: 未找到 {dataset} 数据集")
    
    # 合并所有数据集
    if X_all and y_all:
        X_combined = np.vstack(X_all)
        y_combined = np.vstack(y_all)
        return X_combined, y_combined
    else:
        return None, None

# 示例: 加载多个数据集
datasets_to_load = ['CWRU', 'MFPT', 'KAT']
X_train, y_train = load_datasets(datasets_to_load, 'train')
X_test, y_test = load_datasets(datasets_to_load, 'test')

if X_train is not None and X_test is not None:
    print(f"\n训练数据形状: {X_train.shape}")
    print(f"训练标签形状: {y_train.shape}")
    print(f"测试数据形状: {X_test.shape}")
    print(f"测试标签形状: {y_test.shape}")
```

### KAT、SK和JST数据集加载示例

```python
import numpy as np
import os
import matplotlib.pyplot as plt

# 加载特定数据集
dataset_names = ['KAT', 'SK', 'JST']
results = {}

for dataset in dataset_names:
    # 构建文件路径
    train_data_path = os.path.join(dataset, 'used_npy', 'train', f'{dataset.lower()}_data.npy')
    train_label_path = os.path.join(dataset, 'used_npy', 'train', f'{dataset.lower()}_label.npy')
    
    # 加载训练数据
    X_train = np.load(train_data_path)
    y_train = np.load(train_label_path)
    
    # 获取标签分布
    unique_labels, counts = np.unique(y_train, return_counts=True)
    
    results[dataset] = {
        'X_train_shape': X_train.shape,
        'y_train_shape': y_train.shape,
        'X_train_dtype': X_train.dtype,
        'y_train_dtype': y_train.dtype,
        'label_classes': len(unique_labels),
        'label_distribution': dict(zip(unique_labels, counts))
    }
    
    print(f"\n{dataset} 数据集:")
    print(f"训练数据形状: {X_train.shape}")
    print(f"训练标签形状: {y_train.shape}")
    print(f"数据类型: {X_train.dtype}")
    print(f"标签类型: {y_train.dtype}")
    print(f"标签分布: {dict(zip(unique_labels, counts))}")
    
    # 绘制一个样本的波形示例
    plt.figure(figsize=(10, 3))
    plt.plot(X_train[0])
    plt.title(f"{dataset} 数据集样本示例 (标签: {y_train[0][0]})")
    plt.xlabel('时间点')
    plt.ylabel('振幅')
    plt.tight_layout()
    plt.savefig(f"{dataset.lower()}_sample.png")
    plt.close() 