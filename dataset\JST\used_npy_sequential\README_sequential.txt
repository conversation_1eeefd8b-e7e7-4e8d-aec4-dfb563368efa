JST数据集 - NPY格式数据说明（顺序采样版本）
==========================================

数据格式说明:
- NPY文件格式: 
  * 训练数据: jst_data_sequential.npy (样本矩阵), jst_label_sequential.npy (整数类型标签)
  * 测试数据: jst_data_sequential.npy (样本矩阵), jst_label_sequential.npy (整数类型标签)

标签说明:
- 0: 正常
- 1: 弱水力振动
- 2: 强水力振动

样本数量:
- 训练集: 340 个样本
- 测试集: 340 个样本

采样方法:
- 采用顺序采样方式，按设定的重叠长度依次提取样本
- 训练样本从信号的前50%长度选取，测试样本从后50%长度选取
- 每个样本包含 1024 个数据点，相邻样本重叠 256 个数据点
- 数据位置: 5
