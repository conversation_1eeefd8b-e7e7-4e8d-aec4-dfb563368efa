# 一维振动信号故障诊断数据增强研究平台

基于条件去噪扩散概率模型(CDDPM)的一维振动信号故障诊断数据增强研究平台，支持多种数据增强方法对比实验、智能数据筛选和扩散模型重用功能。

## 📋 项目概述

本项目实现了一个完整的故障诊断数据增强研究平台，基于论文《Data Augmentation Fault Diagnosis of Rolling Machinery Using Condition Denoising Diffusion Probabilistic Model and Improved CNN》开发。项目不仅实现了论文中的CDDPM方法，还集成了多种对比方法，支持全面的对比实验分析。

## 🔥 核心特性

### **🚀 智能实验管理**
- **自动多参数检测**：程序自动识别配置中的列表参数，生成所有组合实验
- **智能扩散模型重用**：相同训练数据下扩散模型只训练一次，节省66.7%以上训练时间
- **缓存配置管理**：每个实验自动生成独立配置文件，保持格式和注释完整

### **🎯 多种数据增强方法**
- **深度学习方法**：CDDPM、CGAN、WGAN、WGAN-GP、DDPM、DCGAN、CVAE
- **传统方法**：ADASYN、SMOTEENN、K-means-SMOTE、RWO-sampling
- **统一接口**：所有方法使用统一的训练和生成接口

### **🔍 智能数据筛选**
- **多层筛选机制**：置信度过滤、Influence评分、异常检测、多样性选择
- **自适应调整**：根据筛选效果自动调整参数
- **质量保证**：显著提升生成样本质量

### **📊 健康样本灵活配置**
- **可选健康样本**：支持包含或排除健康样本的训练
- **数量控制**：精确控制健康样本数量，支持0值表示不使用
- **自动匹配**：健康样本数量可自动匹配故障样本数量

### **🗂️ 多数据集支持**
- **标准数据集**：CWRU、MFPT、JST、KAT、MAFAULDA、SK等
- **自动适配**：自动识别数据集格式和标签结构
- **灵活配置**：支持自定义数据集路径和参数

### **⚡ 完整的实验流程**
- **端到端流程**：数据加载→模型训练→样本生成→智能筛选→性能评估
- **权重损失模式**：支持训练损失、验证损失、权重损失三种最佳模型判断模式
- **早停机制**：智能早停避免过拟合，支持多种监控指标

## 🛠️ 环境要求

- **Python**: 3.8+
- **PyTorch**: 1.12+ (支持CUDA 11.0+)
- **依赖库**: numpy, pandas, matplotlib, scikit-learn, tqdm, pyyaml
- **可选库**: imblearn (传统方法), tensorboard (可视化)

## 📦 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装传统方法依赖（可选）
pip install imbalanced-learn

# 安装可视化依赖（可选）
pip install tensorboard
```

## 🚀 快速开始

### 1. 准备数据

将数据集放置在 `dataset/` 目录下：

```
dataset/
├── CWRU/          # 凯斯西储大学轴承数据
├── MFPT/          # 机械故障预防技术学会数据
├── JST/           # JST数据集
├── KAT/           # KAT数据集
├── SK/            # SK数据集
└── MAFAULDA/      # MAFAULDA数据集
```

### 2. 配置实验参数

#### **单一实验模式**
```yaml
dataset:
  name: "KAT"                           # 单一数据集
  data_loading:
    fault_samples:
      max_fault_samples_per_class: 10   # 单一故障样本数
    healthy_samples:
      max_healthy_samples: 10           # 单一健康样本数

augmentation:
  method: "CDDPM"                       # 单一方法
  num_generated_per_class: 15           # 单一生成数量
```

#### **对比实验模式**
```yaml
dataset:
  name: ["KAT", "SK"]                   # 多数据集对比
  data_loading:
    fault_samples:
      max_fault_samples_per_class: [5, 10, 20]  # 多故障样本数对比
    healthy_samples:
      max_healthy_samples: [0, 5, 10]   # 多健康样本数对比（0表示不使用）

augmentation:
  method: ["CDDPM", "CGAN", "ADASYN"]   # 多方法对比
  num_generated_per_class: [10, 20, 30] # 多生成数量对比
```

### 3. 运行实验

#### **基础运行命令**
```bash
# 完整实验流程（自动检测单一/对比模式）
python main.py --config config.yaml --mode full

# 快速测试模式
python main.py --config config.yaml --mode full --performance-mode fast

# 指定性能模式
python main.py --config config.yaml --mode full --performance-mode high_performance
```

#### **分步运行命令**
```bash
# 只训练扩散模型
python main.py --config config.yaml --mode train_diffusion

# 只训练分类器
python main.py --config config.yaml --mode train_classifier

# 只生成样本
python main.py --config config.yaml --mode generate

# 只评估模型
python main.py --config config.yaml --mode evaluate
```

## 🔥 智能实验管理

### **自动参数检测**
程序自动检测配置中的列表参数，无需手动指定：

```yaml
# 程序自动检测以下参数为对比实验
dataset:
  name: ["KAT", "SK"]                           # 2个数据集
  data_loading:
    fault_samples:
      max_fault_samples_per_class: [5, 10]      # 2个故障样本数
    healthy_samples:
      max_healthy_samples: [0, 5]               # 2个健康样本数

augmentation:
  method: ["CDDPM", "CGAN"]                     # 2个方法
  num_generated_per_class: [10, 20]            # 2个生成数量

# 总实验数：2×2×2×2×2 = 32个实验
```

### **智能扩散模型重用**
相同训练数据配置的实验自动重用扩散模型：

```
实验分组示例：
组1: dataset=KAT, fault=5, healthy=0  → 训练1次扩散模型，重用于4个实验
组2: dataset=KAT, fault=5, healthy=5  → 训练1次扩散模型，重用于4个实验
组3: dataset=KAT, fault=10, healthy=0 → 训练1次扩散模型，重用于4个实验
...

传统方式：32次扩散模型训练
智能重用：8次扩散模型训练 + 24次重用
时间节省：75%
```

### **缓存配置管理**
每个实验自动生成独立的配置文件：

```
cache/
└── 20241226_143022/              # 时间戳目录
    ├── experiment_001_config.yaml
    ├── experiment_002_config.yaml
    └── ...
```

## 📊 配置详解

### **数据集配置**

```yaml
dataset:
  name: "KAT"                           # 数据集名称
  datasets:
    KAT:
      num_classes: 8                    # 类别数量
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]

  data_loading:
    data_type: "sequential"             # 数据类型
    sample_selection: "sequential"      # 样本选择方式
    original_length: 1024               # 原始信号长度
    signal_length: 1024                 # 使用信号长度
    train_val_split: 0.7                # 训练验证分割比例
    normalize: true                     # 是否归一化
    normalization_method: "minmax"      # 归一化方法

    # 故障样本配置
    fault_samples:
      max_fault_samples_per_class: 10   # 每类故障样本数量

    # 健康样本配置
    healthy_samples:
      max_healthy_samples: 10           # 健康样本数量（0=不使用）
      healthy_label: 0                  # 健康样本标签
```

### **数据增强配置**

```yaml
augmentation:
  method: "CDDPM"                       # 增强方法
  num_generated_per_class: 15           # 每类生成样本数
  save_generated: true                  # 保存生成样本
  generate_fault_only: true             # 只生成故障样本

  # 分类器健康样本配置
  classifier_healthy_samples:
    use_real_when_no_generated: true    # 无生成健康样本时使用真实样本
    real_healthy_count: -1              # 真实健康样本数量（-1=自动匹配）

  # 生成策略配置
  generation_strategy:
    target_samples_per_class: -1        # 目标样本数（-1=自动匹配故障样本数）
    initial_multiplier: 5.0             # 初始生成倍数
    adaptive_generation: true           # 自适应生成

  # CDDPM特定参数
  cddpm:
    timesteps: 1000                     # 扩散步数
    beta_schedule: "linear"             # 噪声调度
    unconditional_prob: 0.1             # 无条件训练概率
    guidance_scale: 1.0                 # 引导强度
```

### **训练配置**

```yaml
training:
  # 扩散模型训练（基于论文参数）
  diffusion:
    epochs: 300                         # 训练轮数
    batch_size: 64                      # 批次大小
    learning_rate: 0.00001              # 学习率（1e-5）
    weight_decay: 0.0001                # 权重衰减

    # 最佳模型判断
    best_model_criteria:
      metric: "weighted_loss"           # 判断指标
      mode: "min"                       # 优化方向
      weighted_loss:
        train_weight: 0.7               # 训练损失权重
        val_weight: 0.3                 # 验证损失权重

    # 早停配置
    early_stopping:
      enabled: true                     # 启用早停
      patience: 50                      # 耐心值
      monitor: "weighted_loss"          # 监控指标
      restore_best_weights: true        # 恢复最佳权重

  # 分类器训练
  classifier:
    epochs: 50                          # 训练轮数
    batch_size: 64                      # 批次大小
    learning_rate: 0.0001               # 学习率（1e-4）
    
    best_model_criteria:
      metric: "val_loss"                # 使用验证损失
    
    early_stopping:
      enabled: true
      patience: 20
      monitor: "val_loss"
```

## 🎯 支持的数据增强方法

### **深度学习方法**（需要训练）

| 方法 | 全称 | 特点 | 适用场景 |
|------|------|------|----------|
| **CDDPM** | Conditional Denoising Diffusion Probabilistic Model | 论文主方法，高质量生成 | 主要对比基准 |
| **CGAN** | Conditional Generative Adversarial Network | 经典条件生成方法 | 对比实验 |
| **WGAN** | Wasserstein GAN | 稳定训练的GAN | 对比实验 |
| **WGAN-GP** | WGAN with Gradient Penalty | 改进的WGAN | 对比实验 |
| **DDPM** | Denoising Diffusion Probabilistic Model | 无条件扩散模型 | 对比实验 |
| **DCGAN** | Deep Convolutional GAN | 深度卷积GAN | 对比实验 |
| **CVAE** | Conditional Variational Autoencoder | 条件变分自编码器 | 对比实验 |

### **传统方法**（无需训练）

| 方法 | 全称 | 特点 | 适用场景 |
|------|------|------|----------|
| **ADASYN** | Adaptive Synthetic Sampling | 自适应合成采样 | 快速对比 |
| **SMOTEENN** | SMOTE + Edited Nearest Neighbours | SMOTE与清理结合 | 传统基准 |
| **K-means-SMOTE** | K-means SMOTE | 基于聚类的SMOTE | 改进SMOTE |
| **RWO-sampling** | Random Walk Oversampling | 随机游走过采样 | 简单有效 |

## 📁 项目结构

```
├── main.py                           # 主程序入口
├── config.yaml                       # 主配置文件
├── requirements.txt                  # 依赖包列表
├── README.md                         # 项目说明文档
├──
├── common/                           # 公共模块
│   ├── __init__.py
│   ├── data_loader.py                # 数据加载器（支持健康样本配置）
│   ├── experiment_manager.py         # 智能实验管理器
│   ├── results_manager.py            # 结果管理器
│   ├── performance_manager.py        # 性能管理器
│   ├── data_screening.py             # 数据筛选流水线
│   ├── adaptive_screening.py         # 自适应筛选
│   ├── metrics.py                    # 评估指标计算
│   ├── visualization.py              # 可视化工具
│   └── utils.py                      # 工具函数
│
├── models/                           # 模型定义
│   ├── __init__.py
│   ├── cddpm.py                      # CDDPM模型实现
│   ├── mr_cnn.py                     # MRCNN分类器
│   ├── unet.py                       # UNet1D网络
│   ├── augmentation_factory.py       # 数据增强工厂
│   ├── augmentation_methods.py       # 深度学习增强方法
│   └── traditional_augmentation.py   # 传统增强方法
│
├── dataset/                          # 数据集目录
│   ├── README.md                     # 数据集说明
│   ├── CWRU/                         # 凯斯西储大学数据
│   ├── MFPT/                         # MFPT数据
│   ├── JST/                          # JST数据
│   ├── KAT/                          # KAT数据
│   ├── SK/                           # SK数据
│   └── MAFAULDA/                     # MAFAULDA数据
│
├── data_preprocessing/               # 数据预处理脚本
│   ├── S1_1_process_MFPT_dataset.m
│   ├── S1_2_process_SK_dataset.m
│   ├── S1_3_process_JST_dataset.m
│   ├── S1_4_process_MAFAULDA_dataset.m
│   ├── S1_5_process_KAT_dataset.m
│   └── S1_6_process_CWRU_dataset.m
│
├── paper/                            # 论文相关文档
│   ├── Data_Augmentation_Fault_Diagnosis_*.pdf
│   └── MD/                           # 论文参数说明
│
├── results/                          # 结果保存目录
│   └── {dataset_name}/               # 按数据集分类
│       └── {timestamp}/              # 按时间戳分类
│           ├── individual_results/   # 单次实验结果
│           ├── comparison_results.csv # 对比实验汇总
│           ├── plots_data.csv        # 绘图数据
│           └── config_cache/         # 配置文件缓存
│
├── checkpoints/                      # 模型检查点
│   ├── diffusion/                    # 扩散模型检查点
│   ├── classifier/                   # 分类器检查点
│   └── augmentation/                 # 增强方法检查点
│
├── cache/                            # 实验缓存
│   └── {timestamp}/                  # 按时间戳缓存
│       ├── experiment_001_config.yaml
│       ├── experiment_002_config.yaml
│       └── ...
│
├── logs/                             # 日志文件
├── generated_samples/                # 生成样本保存
└── docs/                             # 文档目录
```

## 📊 结果分析

### **结果目录结构**

```
results/
└── KAT/                              # 数据集名称
    └── 20241226_143022/              # 实验时间戳
        ├── individual_results/       # 单次实验结果
        │   ├── experiment_001/       # 实验1结果
        │   │   ├── results.json      # 性能指标
        │   │   ├── training_curves.png # 训练曲线
        │   │   ├── confusion_matrix.png # 混淆矩阵
        │   │   ├── generated_samples.png # 生成样本
        │   │   ├── config.yaml       # 实验配置
        │   │   └── plots_data/       # 绘图数据CSV
        │   └── experiment_002/       # 实验2结果
        │       └── ...
        ├── comparison_results.csv     # 对比实验汇总
        ├── plots_data.csv            # 所有绘图数据
        └── experiment_summary.log    # 实验总结日志
```

### **性能指标**

程序自动计算并保存以下指标：

#### **分类性能指标**
- **Accuracy**: 总体准确率
- **Precision**: 精确率（每类别）
- **Recall**: 召回率（每类别）
- **F1-Score**: F1分数（每类别）
- **Confusion Matrix**: 混淆矩阵

#### **生成质量指标**
- **GAN-train**: 在训练集上训练分类器的准确率
- **GAN-test**: 在测试集上评估的准确率
- **IS (Inception Score)**: 生成样本质量评分
- **FID (Fréchet Inception Distance)**: 生成样本与真实样本的距离

#### **训练过程指标**
- **Training Loss**: 训练损失曲线
- **Validation Loss**: 验证损失曲线
- **Training Time**: 训练时间统计
- **Model Parameters**: 模型参数数量

### **CSV数据格式**

#### **comparison_results.csv**
```csv
experiment_id,dataset,method,fault_samples,healthy_samples,generated_samples,accuracy,precision,recall,f1_score,gan_train,gan_test,training_time
1,KAT,CDDPM,10,5,15,0.9234,0.9156,0.9234,0.9189,0.8876,0.8654,1234.56
2,KAT,CGAN,10,5,15,0.8987,0.8923,0.8987,0.8954,0.8543,0.8321,987.65
...
```

#### **plots_data.csv**
```csv
experiment_id,epoch,train_loss,val_loss,train_acc,val_acc
1,1,2.1234,2.0987,0.3456,0.3789
1,2,1.9876,1.9543,0.4123,0.4567
...
```

## 🚀 实验示例

### **示例1：健康样本影响分析**

```yaml
# 配置文件：test_healthy_samples.yaml
dataset:
  name: "KAT"
  data_loading:
    fault_samples:
      max_fault_samples_per_class: 10   # 固定故障样本数
    healthy_samples:
      max_healthy_samples: [0, 5, 10, 15] # 对比不同健康样本数

augmentation:
  method: "CDDPM"
  num_generated_per_class: 15
  generate_fault_only: true             # 只生成故障样本
```

**运行命令**：
```bash
python main.py --config test_healthy_samples.yaml --mode full
```

**预期结果**：4个实验，分析健康样本数量对分类性能的影响

### **示例2：多方法对比实验**

```yaml
# 配置文件：test_methods_comparison.yaml
dataset:
  name: "KAT"
  data_loading:
    fault_samples:
      max_fault_samples_per_class: 10
    healthy_samples:
      max_healthy_samples: 0             # 不使用健康样本

augmentation:
  method: ["CDDPM", "CGAN", "WGAN", "ADASYN", "SMOTEENN"]
  num_generated_per_class: [10, 20, 30]
  generate_fault_only: true
```

**运行命令**：
```bash
python main.py --config test_methods_comparison.yaml --mode full
```

**预期结果**：15个实验（5方法×3生成数量），全面对比不同方法的性能

### **示例3：数据集对比实验**

```yaml
# 配置文件：test_datasets_comparison.yaml
dataset:
  name: ["KAT", "SK", "JST"]            # 多数据集对比
  data_loading:
    fault_samples:
      max_fault_samples_per_class: [5, 10]
    healthy_samples:
      max_healthy_samples: 0

augmentation:
  method: "CDDPM"
  num_generated_per_class: 15
```

**运行命令**：
```bash
python main.py --config test_datasets_comparison.yaml --mode full
```

**预期结果**：6个实验（3数据集×2故障样本数），对比不同数据集的表现

## 🎯 总结

本项目提供了一个**完整的振动信号故障诊断数据增强研究平台**，不仅实现了论文中的CDDPM方法，还集成了多种对比方法，支持全面的对比实验分析。通过智能的实验管理系统和高效的训练策略，研究人员可以轻松进行大规模对比实验，深入分析不同方法的性能表现。

**核心优势**:
- 🚀 **高效**: 智能重用机制节省75%训练时间
- 🎯 **全面**: 10+种方法全面对比分析
- 🔧 **灵活**: 支持多种配置和性能模式
- 📊 **专业**: 完整的评估指标和可视化
- 🛠️ **易用**: 自动参数检测和配置管理

**适用场景**:
- ✅ 振动信号故障诊断研究
- ✅ 数据增强方法对比分析
- ✅ 小样本学习问题研究
- ✅ 生成模型质量评估
- ✅ 深度学习方法验证

欢迎研究人员使用本平台进行相关研究，如有问题请查看故障排除部分或提交Issue。
