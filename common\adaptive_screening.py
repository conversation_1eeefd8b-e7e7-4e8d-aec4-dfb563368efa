"""
自适应数据筛选模块
确保最终生成的数据个数与目标设置一致
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from .data_screening import DataScreening<PERSON>ipeline, ConfidenceFilter, InfluenceFilter, OutlierDetector, DiversitySelector

logger = logging.getLogger(__name__)


class AdaptiveDataGenerator:
    """自适应数据生成器，确保生成足够的样本用于筛选"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.generation_config = config['augmentation']['generation_strategy']
        self.target_samples_per_class_raw = self.generation_config['target_samples_per_class']
        self.initial_multiplier = self.generation_config['initial_multiplier']
        self.min_multiplier = self.generation_config['min_multiplier']
        self.max_multiplier = self.generation_config['max_multiplier']
        self.adaptive_generation = self.generation_config['adaptive_generation']

        # 处理target_samples_per_class的自动调整
        self.target_samples_per_class = self._resolve_target_samples_per_class()

    def _resolve_target_samples_per_class(self) -> int:
        """
        解析target_samples_per_class配置

        Returns:
            解析后的目标样本数
        """
        if self.target_samples_per_class_raw == -1:
            # -1表示与故障样本数量保持一致
            fault_samples_config = self.config['dataset']['data_loading']['fault_samples']
            max_fault_samples = fault_samples_config.get('max_fault_samples_per_class', 10)

            # 如果是列表，取第一个值
            if isinstance(max_fault_samples, list):
                target_samples = max_fault_samples[0]
            else:
                target_samples = max_fault_samples

            logger.info(f"生成策略目标样本数设置为-1，自动匹配故障样本数量: {target_samples}")
            return target_samples
        else:
            # 使用指定值
            return self.target_samples_per_class_raw
        
    def calculate_generation_count(self, target_per_class: int, 
                                 screening_history: Optional[List[Dict]] = None) -> int:
        """
        计算需要生成的样本数量
        
        Args:
            target_per_class: 每个类别的目标样本数
            screening_history: 历史筛选记录
            
        Returns:
            每个类别需要生成的样本数
        """
        if not self.adaptive_generation or screening_history is None:
            # 使用初始倍数
            return int(target_per_class * self.initial_multiplier)
        
        # 基于历史筛选效果调整生成数量
        avg_retention_rate = np.mean([h['overall_retention_rate'] for h in screening_history])
        
        if avg_retention_rate > 0:
            # 根据平均保留率计算需要的倍数
            required_multiplier = 1.0 / avg_retention_rate
            # 添加安全边际
            required_multiplier *= 1.2
            
            # 限制在合理范围内
            required_multiplier = max(self.min_multiplier, 
                                    min(self.max_multiplier, required_multiplier))
        else:
            required_multiplier = self.max_multiplier
        
        generation_count = int(target_per_class * required_multiplier)
        
        logger.info(f"自适应生成数量计算:")
        logger.info(f"  目标样本数: {target_per_class}")
        logger.info(f"  历史平均保留率: {avg_retention_rate:.2%}")
        logger.info(f"  计算倍数: {required_multiplier:.2f}")
        logger.info(f"  生成数量: {generation_count}")
        
        return generation_count


class AdaptiveScreeningPipeline:
    """自适应筛选流水线，确保最终样本数量符合目标"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.screening_config = config['data_screening']
        self.target_control = self.screening_config['target_control']
        self.enabled = self.screening_config['enabled']
        
        # 目标数量控制
        self.strict_target = self.target_control['strict_target']
        self.fallback_strategy = self.target_control['fallback_strategy']
        
        # 初始化筛选器
        if self.enabled:
            self._init_filters()
            
        logger.info(f"自适应筛选流水线已初始化")
        logger.info(f"  严格目标控制: {self.strict_target}")
        logger.info(f"  回退策略: {self.fallback_strategy}")
    
    def _init_filters(self):
        """初始化各个筛选器"""
        conf_config = self.screening_config['confidence_filter']
        if conf_config['enabled']:
            self.confidence_filter = AdaptiveConfidenceFilter(self.config)
        else:
            self.confidence_filter = None
            
        infl_config = self.screening_config['influence_filter']
        if infl_config['enabled']:
            self.influence_filter = AdaptiveInfluenceFilter(self.config)
        else:
            self.influence_filter = None
            
        outlier_config = self.screening_config['outlier_detection']
        if outlier_config['enabled']:
            self.outlier_detector = OutlierDetector(self.config)
        else:
            self.outlier_detector = None
            
        div_config = self.screening_config['diversity_selection']
        if div_config['enabled']:
            self.diversity_selector = AdaptiveDiversitySelector(self.config)
        else:
            self.diversity_selector = None
    
    def screen_to_target(self, classifier: nn.Module, generated_data: torch.Tensor,
                        generated_labels: torch.Tensor, val_data: torch.Tensor,
                        val_labels: torch.Tensor, target_per_class: int,
                        device: torch.device) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
        """
        筛选数据到目标数量
        
        Args:
            classifier: 训练好的分类器
            generated_data: 生成的数据
            generated_labels: 生成数据的标签
            val_data: 验证数据
            val_labels: 验证标签
            target_per_class: 每个类别的目标样本数
            device: 计算设备
            
        Returns:
            screened_data: 筛选后的数据
            screened_labels: 筛选后的标签
            screening_stats: 筛选统计信息
        """
        if not self.enabled:
            logger.info("自适应筛选未启用，返回原始生成数据")
            return self._select_target_samples(generated_data, generated_labels, target_per_class)
        
        logger.info("=" * 50)
        logger.info("开始自适应数据筛选流水线")
        logger.info(f"目标样本数: 每类 {target_per_class} 个")
        logger.info("=" * 50)
        
        current_data = generated_data.clone()
        current_labels = generated_labels.clone()
        
        # 记录筛选统计
        screening_stats = {
            'screening_enabled': True,
            'target_per_class': target_per_class,
            'original_count': len(generated_data),
            'steps': []
        }
        
        # 计算总目标数量
        num_classes = len(torch.unique(generated_labels))
        total_target = target_per_class * num_classes
        
        # 步骤1: 置信度过滤（自适应）
        if self.confidence_filter:
            logger.info("步骤1: 自适应置信度过滤")
            before_count = len(current_data)
            current_data, current_labels = self.confidence_filter.adaptive_filter(
                classifier, current_data, current_labels, total_target, device
            )
            after_count = len(current_data)
            
            screening_stats['steps'].append({
                'step': 'adaptive_confidence_filter',
                'before_count': before_count,
                'after_count': after_count,
                'retention_rate': after_count / before_count if before_count > 0 else 0
            })
        
        # 步骤2: Influence评分过滤（自适应）
        if self.influence_filter and len(current_data) > 0:
            logger.info("步骤2: 自适应Influence评分过滤")
            before_count = len(current_data)
            current_data, current_labels = self.influence_filter.adaptive_filter(
                classifier, current_data, current_labels, val_data, val_labels, 
                total_target, device
            )
            after_count = len(current_data)
            
            screening_stats['steps'].append({
                'step': 'adaptive_influence_filter',
                'before_count': before_count,
                'after_count': after_count,
                'retention_rate': after_count / before_count if before_count > 0 else 0
            })
        
        # 步骤3: 离群检测（如果启用）
        if self.outlier_detector and len(current_data) > 0:
            logger.info("步骤3: 离群检测")
            before_count = len(current_data)
            current_data, current_labels = self.outlier_detector.detect(
                classifier, current_data, current_labels, device
            )
            after_count = len(current_data)
            
            screening_stats['steps'].append({
                'step': 'outlier_detection',
                'before_count': before_count,
                'after_count': after_count,
                'retention_rate': after_count / before_count if before_count > 0 else 0
            })
        
        # 步骤4: 精确数量选择
        if len(current_data) > 0:
            logger.info("步骤4: 精确目标数量选择")
            before_count = len(current_data)
            
            if self.diversity_selector:
                current_data, current_labels = self.diversity_selector.select_exact_count(
                    classifier, current_data, current_labels, target_per_class, device
                )
            else:
                current_data, current_labels = self._select_target_samples(
                    current_data, current_labels, target_per_class
                )
            
            after_count = len(current_data)
            
            screening_stats['steps'].append({
                'step': 'exact_count_selection',
                'before_count': before_count,
                'after_count': after_count,
                'retention_rate': after_count / before_count if before_count > 0 else 0
            })
        
        # 检查是否达到目标数量
        final_count = len(current_data)
        if final_count < total_target and self.strict_target:
            logger.warning(f"筛选后样本不足: {final_count} < {total_target}")
            current_data, current_labels = self._apply_fallback_strategy(
                current_data, current_labels, generated_data, generated_labels, 
                target_per_class
            )
            final_count = len(current_data)
        
        # 最终统计
        screening_stats['final_count'] = final_count
        screening_stats['target_achieved'] = final_count >= total_target
        screening_stats['overall_retention_rate'] = (
            final_count / len(generated_data) if len(generated_data) > 0 else 0
        )
        
        logger.info("=" * 50)
        logger.info("自适应数据筛选流水线完成")
        logger.info(f"原始样本数: {screening_stats['original_count']}")
        logger.info(f"目标样本数: {total_target}")
        logger.info(f"筛选后样本数: {screening_stats['final_count']}")
        logger.info(f"目标达成: {'是' if screening_stats['target_achieved'] else '否'}")
        logger.info("=" * 50)
        
        return current_data, current_labels, screening_stats
    
    def _select_target_samples(self, data: torch.Tensor, labels: torch.Tensor, 
                              target_per_class: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """按类别选择目标数量的样本"""
        selected_indices = []
        
        for class_idx in torch.unique(labels):
            class_mask = labels == class_idx
            class_indices = torch.where(class_mask)[0]
            
            if len(class_indices) >= target_per_class:
                # 随机选择目标数量
                selected_class_indices = torch.randperm(len(class_indices))[:target_per_class]
                selected_indices.extend(class_indices[selected_class_indices].tolist())
            else:
                # 全部选择
                selected_indices.extend(class_indices.tolist())
        
        return data[selected_indices], labels[selected_indices]
    
    def _apply_fallback_strategy(self, current_data: torch.Tensor, current_labels: torch.Tensor,
                               original_data: torch.Tensor, original_labels: torch.Tensor,
                               target_per_class: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """应用回退策略"""
        logger.info(f"应用回退策略: {self.fallback_strategy}")

        if self.fallback_strategy == "random_fill":
            # 从原始数据中随机补充
            return self._random_fill_samples(current_data, current_labels,
                                           original_data, original_labels, target_per_class)
        elif self.fallback_strategy == "relax_thresholds":
            # 放宽筛选条件（这里简化为随机补充）
            return self._random_fill_samples(current_data, current_labels,
                                           original_data, original_labels, target_per_class)
        else:
            # 默认返回当前数据
            return current_data, current_labels

    def _random_fill_samples(self, current_data: torch.Tensor, current_labels: torch.Tensor,
                           original_data: torch.Tensor, original_labels: torch.Tensor,
                           target_per_class: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """随机补充样本到目标数量"""
        # 计算每个类别还需要多少样本
        current_counts = {}
        for class_idx in torch.unique(original_labels):
            class_idx = class_idx.item()
            current_count = (current_labels == class_idx).sum().item()
            current_counts[class_idx] = current_count

        additional_indices = []

        for class_idx, current_count in current_counts.items():
            needed = target_per_class - current_count
            if needed > 0:
                # 从原始数据中找到该类别的样本
                class_mask = original_labels == class_idx
                available_indices = torch.where(class_mask)[0]

                # 排除已经选择的样本
                current_class_mask = current_labels == class_idx
                if current_class_mask.any():
                    # 这里简化处理，直接随机选择
                    if len(available_indices) >= needed:
                        selected = torch.randperm(len(available_indices))[:needed]
                        additional_indices.extend(available_indices[selected].tolist())
                    else:
                        additional_indices.extend(available_indices.tolist())

        if additional_indices:
            additional_data = original_data[additional_indices]
            additional_labels = original_labels[additional_indices]

            combined_data = torch.cat([current_data, additional_data], dim=0)
            combined_labels = torch.cat([current_labels, additional_labels], dim=0)

            return combined_data, combined_labels

        return current_data, current_labels


class AdaptiveConfidenceFilter(ConfidenceFilter):
    """自适应置信度过滤器"""

    def __init__(self, config: Dict):
        super().__init__(config)
        self.adaptive_config = config['data_screening']['confidence_filter']
        self.min_threshold = self.adaptive_config['min_threshold']
        self.max_threshold = self.adaptive_config['max_threshold']
        self.adaptive = self.adaptive_config['adaptive']

    def adaptive_filter(self, classifier: nn.Module, data: torch.Tensor,
                       labels: torch.Tensor, target_count: int,
                       device: torch.device) -> Tuple[torch.Tensor, torch.Tensor]:
        """自适应置信度过滤"""
        if not self.adaptive:
            return self.filter(classifier, data, labels, device)

        # 尝试不同的阈值，找到最接近目标数量的结果
        best_threshold = self.threshold
        best_data, best_labels = None, None
        best_diff = float('inf')

        # 测试不同阈值
        test_thresholds = np.linspace(self.min_threshold, self.max_threshold, 10)

        for test_threshold in test_thresholds:
            self.threshold = test_threshold
            filtered_data, filtered_labels = self.filter(classifier, data, labels, device)

            diff = abs(len(filtered_data) - target_count)
            if diff < best_diff:
                best_diff = diff
                best_threshold = test_threshold
                best_data, best_labels = filtered_data, filtered_labels

        # 恢复最佳阈值
        self.threshold = best_threshold

        logger.info(f"自适应置信度过滤: 最佳阈值 {best_threshold:.3f}, "
                   f"筛选结果 {len(best_data)}/{target_count}")

        return best_data, best_labels


class AdaptiveInfluenceFilter(InfluenceFilter):
    """自适应Influence过滤器"""

    def __init__(self, config: Dict):
        super().__init__(config)
        self.adaptive_config = config['data_screening']['influence_filter']
        self.min_ratio = self.adaptive_config['min_ratio']
        self.max_ratio = self.adaptive_config['max_ratio']
        self.adaptive = self.adaptive_config['adaptive']

    def adaptive_filter(self, classifier: nn.Module, candidate_data: torch.Tensor,
                       candidate_labels: torch.Tensor, val_data: torch.Tensor,
                       val_labels: torch.Tensor, target_count: int,
                       device: torch.device) -> Tuple[torch.Tensor, torch.Tensor]:
        """自适应Influence过滤"""
        if not self.adaptive:
            return self.filter(classifier, candidate_data, candidate_labels,
                             val_data, val_labels, device)

        # 尝试不同的剔除比例
        best_ratio = self.ratio
        best_data, best_labels = None, None
        best_diff = float('inf')

        # 测试不同比例
        test_ratios = np.linspace(self.min_ratio, self.max_ratio, 8)

        for test_ratio in test_ratios:
            self.ratio = test_ratio
            filtered_data, filtered_labels = self.filter(
                classifier, candidate_data, candidate_labels,
                val_data, val_labels, device
            )

            diff = abs(len(filtered_data) - target_count)
            if diff < best_diff:
                best_diff = diff
                best_ratio = test_ratio
                best_data, best_labels = filtered_data, filtered_labels

        # 恢复最佳比例
        self.ratio = best_ratio

        logger.info(f"自适应Influence过滤: 最佳剔除比例 {best_ratio:.3f}, "
                   f"筛选结果 {len(best_data)}/{target_count}")

        return best_data, best_labels


class AdaptiveDiversitySelector(DiversitySelector):
    """自适应多样性选择器"""

    def __init__(self, config: Dict):
        super().__init__(config)
        self.adaptive_config = config['data_screening']['diversity_selection']
        self.use_target_count = self.adaptive_config['use_target_count']

    def select_exact_count(self, classifier: nn.Module, data: torch.Tensor,
                          labels: torch.Tensor, target_per_class: int,
                          device: torch.device) -> Tuple[torch.Tensor, torch.Tensor]:
        """精确选择目标数量的样本"""
        if not self.use_target_count:
            return self.select(classifier, data, labels, device)

        logger.info(f"精确数量选择: 每类 {target_per_class} 个样本")

        selected_indices = []

        # 按类别处理
        for class_idx in torch.unique(labels):
            class_mask = labels == class_idx
            class_data = data[class_mask]
            class_indices = torch.where(class_mask)[0]

            if len(class_data) == 0:
                continue

            if len(class_data) <= target_per_class:
                # 如果该类别样本不足，全部选择
                selected_indices.extend(class_indices.tolist())
            else:
                # 使用聚类选择代表性样本
                if self.method == "kmeans":
                    class_selected = self._kmeans_selection_exact(
                        classifier, class_data, class_indices, target_per_class, device
                    )
                else:
                    # 随机选择
                    perm = torch.randperm(len(class_indices))[:target_per_class]
                    class_selected = class_indices[perm].tolist()

                selected_indices.extend(class_selected)

        selected_data = data[selected_indices]
        selected_labels = labels[selected_indices]

        # 验证每个类别的数量
        final_counts = {}
        for class_idx in torch.unique(labels):
            class_idx = class_idx.item()
            count = (selected_labels == class_idx).sum().item()
            final_counts[class_idx] = count

        logger.info(f"精确选择完成: {final_counts}")

        return selected_data, selected_labels

    def _kmeans_selection_exact(self, classifier: nn.Module, class_data: torch.Tensor,
                               class_indices: torch.Tensor, target_count: int,
                               device: torch.device) -> List[int]:
        """对单个类别进行K-means精确选择"""
        # 提取特征
        features = self._extract_features(classifier, class_data, device)

        # 标准化特征
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)

        # K-means聚类
        from sklearn.cluster import KMeans
        kmeans = KMeans(n_clusters=target_count, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(features_scaled)

        # 选择每个聚类的中心点
        selected_indices = []
        for cluster_id in range(target_count):
            cluster_mask = cluster_labels == cluster_id
            if not cluster_mask.any():
                continue

            cluster_indices = np.where(cluster_mask)[0]
            cluster_center = kmeans.cluster_centers_[cluster_id]
            cluster_features = features_scaled[cluster_mask]

            # 找到距离聚类中心最近的点
            distances = np.linalg.norm(cluster_features - cluster_center, axis=1)
            medoid_idx = cluster_indices[np.argmin(distances)]
            selected_indices.append(class_indices[medoid_idx].item())

        return selected_indices
