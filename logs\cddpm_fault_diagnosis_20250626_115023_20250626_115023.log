2025-06-26 11:50:23,779 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_20250626_115023_20250626_115023.log
2025-06-26 11:50:23,779 - __main__ - INFO - ================================================================================
2025-06-26 11:50:23,779 - __main__ - INFO - 开始单一实验: cddpm_fault_diagnosis
2025-06-26 11:50:23,780 - __main__ - INFO - 数据集: KAT
2025-06-26 11:50:23,780 - __main__ - INFO - ================================================================================
2025-06-26 11:50:23,780 - __main__ - INFO - 🚀 实验开始
2025-06-26 11:50:23,780 - __main__ - INFO - ================================================================================
2025-06-26 11:50:23,780 - __main__ - INFO - 当前实验配置
2025-06-26 11:50:23,780 - __main__ - INFO - ================================================================================
2025-06-26 11:50:23,780 - __main__ - INFO - 数据集: KAT
2025-06-26 11:50:23,780 - __main__ - INFO - 故障样本每类: [3]
2025-06-26 11:50:23,780 - __main__ - INFO - 健康样本总数: -1
2025-06-26 11:50:23,780 - __main__ - INFO - 信号长度: 1024
2025-06-26 11:50:23,780 - __main__ - INFO - 归一化方法: minmax
2025-06-26 11:50:23,780 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 11:50:23,780 - __main__ - INFO - 每类生成样本数: [3]
2025-06-26 11:50:23,780 - __main__ - INFO - 只生成故障样本: False
2025-06-26 11:50:23,780 - __main__ - INFO - 扩散模型训练轮数: 10000
2025-06-26 11:50:23,780 - __main__ - INFO - 分类器训练轮数: 100
2025-06-26 11:50:23,780 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 11:50:23,780 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 11:50:23,780 - __main__ - INFO - 设备: auto
2025-06-26 11:50:23,780 - __main__ - INFO - 性能模式: high_performance
2025-06-26 11:50:23,780 - __main__ - INFO - 随机种子: 42
2025-06-26 11:50:23,780 - __main__ - INFO - ================================================================================
2025-06-26 11:50:23,780 - __main__ - INFO - ============================================================
2025-06-26 11:50:23,780 - __main__ - INFO - 健康样本配置验证
2025-06-26 11:50:23,780 - __main__ - INFO - ============================================================
2025-06-26 11:50:23,780 - __main__ - INFO - 扩散训练健康样本数量: -1
2025-06-26 11:50:23,780 - __main__ - INFO - 只生成故障样本: False
2025-06-26 11:50:23,780 - __main__ - INFO - 分类器使用真实健康样本: True
2025-06-26 11:50:23,781 - __main__ - INFO - 真实健康样本数量: -1
2025-06-26 11:50:23,781 - __main__ - INFO - ✅ 健康样本配置验证通过
2025-06-26 11:50:23,781 - __main__ - INFO - ============================================================
2025-06-26 11:50:23,781 - __main__ - INFO - 使用设备: cuda
2025-06-26 11:50:23,782 - __main__ - INFO - 加载数据...
2025-06-26 11:50:23,782 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 11:50:23,782 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 11:50:23,788 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 11:50:23,793 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 11:50:23,793 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 11:50:23,793 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 11:50:23,793 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 11:50:23,793 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 11:50:23,793 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 11:50:23,793 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 11:50:23,793 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 11:50:23,794 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 11:50:23,794 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 11:50:23,794 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 11:50:23,794 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 11:50:23,794 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 11:50:23,794 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 11:50:23,798 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 11:50:23,802 - common.data_loader - INFO - 数据加载完成:
2025-06-26 11:50:23,802 - common.data_loader - INFO -   训练样本: 24
2025-06-26 11:50:23,802 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 11:50:23,802 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 11:50:23,802 - common.data_loader - INFO -   类别数: 8
2025-06-26 11:50:23,803 - __main__ - INFO - ==================================================
2025-06-26 11:50:23,803 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 11:50:23,803 - __main__ - INFO - ==================================================
2025-06-26 11:50:23,804 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 11:50:23,804 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 11:50:23,804 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 11:50:23,804 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 11:50:23,804 - models.cddpm - INFO -   类别数量: 8
2025-06-26 11:50:23,979 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 11:50:23,979 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 11:50:23,979 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 11:50:23,979 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 11:50:24,237 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 11:50:24,237 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 11:50:24,237 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共10000轮
2025-06-26 11:50:31,281 - models.augmentation_factory - INFO - Epoch   1/10000: Train Loss: 0.835234, Val Loss: 0.853776, Weighted Loss: 0.840797 (Best✓)
2025-06-26 11:50:31,811 - models.augmentation_factory - INFO - Epoch   3/10000: Train Loss: 0.819030, Val Loss: 0.803705, Weighted Loss: 0.814432 (Best✓)
2025-06-26 11:50:32,062 - models.augmentation_factory - INFO - Epoch   4/10000: Train Loss: 0.810887, Val Loss: 0.810842, Weighted Loss: 0.810873 (Best✓)
2025-06-26 11:50:32,311 - models.augmentation_factory - INFO - Epoch   5/10000: Train Loss: 0.812245, Val Loss: 0.804304, Weighted Loss: 0.809863 (Best✓)
2025-06-26 11:50:32,560 - models.augmentation_factory - INFO - Epoch   6/10000: Train Loss: 0.810429, Val Loss: 0.797167, Weighted Loss: 0.806450 (Best✓)
2025-06-26 11:50:32,808 - models.augmentation_factory - INFO - Epoch   7/10000: Train Loss: 0.804505, Val Loss: 0.801157, Weighted Loss: 0.803501 (Best✓)
2025-06-26 11:50:33,307 - models.augmentation_factory - INFO - Epoch   9/10000: Train Loss: 0.798678, Val Loss: 0.814325, Weighted Loss: 0.803372 (Best✓)
2025-06-26 11:50:33,558 - models.augmentation_factory - INFO - Epoch  10/10000: Train Loss: 0.804351, Val Loss: 0.798371, Weighted Loss: 0.802557 (Best✓)
2025-06-26 11:50:33,810 - models.augmentation_factory - INFO - Epoch  11/10000: Train Loss: 0.802329, Val Loss: 0.793688, Weighted Loss: 0.799737 (Best✓)
2025-06-26 11:50:34,078 - models.augmentation_factory - INFO - Epoch  12/10000: Train Loss: 0.802973, Val Loss: 0.789175, Weighted Loss: 0.798833 (Best✓)
2025-06-26 11:50:34,591 - models.augmentation_factory - INFO - Epoch  14/10000: Train Loss: 0.797032, Val Loss: 0.788962, Weighted Loss: 0.794611 (Best✓)
2025-06-26 11:50:35,835 - models.augmentation_factory - INFO - Epoch  19/10000: Train Loss: 0.792857, Val Loss: 0.770299, Weighted Loss: 0.786090 (Best✓)
2025-06-26 11:50:36,339 - models.augmentation_factory - INFO - Epoch  21/10000: Train Loss: 0.781906, Val Loss: 0.780221, Weighted Loss: 0.781400 (Best✓)
2025-06-26 11:50:36,610 - models.augmentation_factory - INFO - Epoch  22/10000: Train Loss: 0.770954, Val Loss: 0.775157, Weighted Loss: 0.772215 (Best✓)
2025-06-26 11:50:36,869 - models.augmentation_factory - INFO - Epoch  23/10000: Train Loss: 0.774772, Val Loss: 0.748217, Weighted Loss: 0.766805 (Best✓)
2025-06-26 11:50:37,120 - models.augmentation_factory - INFO - Epoch  24/10000: Train Loss: 0.776729, Val Loss: 0.733185, Weighted Loss: 0.763666 (Best✓)
2025-06-26 11:50:37,370 - models.augmentation_factory - INFO - Epoch  25/10000: Train Loss: 0.748228, Val Loss: 0.696973, Weighted Loss: 0.732852 (Best✓)
2025-06-26 11:50:37,622 - models.augmentation_factory - INFO - Epoch  26/10000: Train Loss: 0.727016, Val Loss: 0.685382, Weighted Loss: 0.714526 (Best✓)
2025-06-26 11:50:37,874 - models.augmentation_factory - INFO - Epoch  27/10000: Train Loss: 0.686583, Val Loss: 0.669095, Weighted Loss: 0.681337 (Best✓)
2025-06-26 11:50:38,129 - models.augmentation_factory - INFO - Epoch  28/10000: Train Loss: 0.685382, Val Loss: 0.671225, Weighted Loss: 0.681135 (Best✓)
2025-06-26 11:50:38,378 - models.augmentation_factory - INFO - Epoch  29/10000: Train Loss: 0.654427, Val Loss: 0.628650, Weighted Loss: 0.646694 (Best✓)
2025-06-26 11:50:38,628 - models.augmentation_factory - INFO - Epoch  30/10000: Train Loss: 0.646601, Val Loss: 0.644057, Weighted Loss: 0.645838 (Best✓)
2025-06-26 11:50:38,876 - models.augmentation_factory - INFO - Epoch  31/10000: Train Loss: 0.639836, Val Loss: 0.606300, Weighted Loss: 0.629775 (Best✓)
2025-06-26 11:50:39,126 - models.augmentation_factory - INFO - Epoch  32/10000: Train Loss: 0.621291, Val Loss: 0.621351, Weighted Loss: 0.621309 (Best✓)
2025-06-26 11:50:39,637 - models.augmentation_factory - INFO - Epoch  34/10000: Train Loss: 0.593362, Val Loss: 0.571619, Weighted Loss: 0.586839 (Best✓)
2025-06-26 11:50:39,914 - models.augmentation_factory - INFO - Epoch  35/10000: Train Loss: 0.593492, Val Loss: 0.532164, Weighted Loss: 0.575094 (Best✓)
2025-06-26 11:50:40,165 - models.augmentation_factory - INFO - Epoch  36/10000: Train Loss: 0.567107, Val Loss: 0.541173, Weighted Loss: 0.559327 (Best✓)
2025-06-26 11:50:40,426 - models.augmentation_factory - INFO - Epoch  37/10000: Train Loss: 0.541350, Val Loss: 0.489714, Weighted Loss: 0.525859 (Best✓)
2025-06-26 11:50:40,681 - models.augmentation_factory - INFO - Epoch  38/10000: Train Loss: 0.528309, Val Loss: 0.435476, Weighted Loss: 0.500459 (Best✓)
2025-06-26 11:50:40,930 - models.augmentation_factory - INFO - Epoch  39/10000: Train Loss: 0.507264, Val Loss: 0.436852, Weighted Loss: 0.486141 (Best✓)
2025-06-26 11:50:41,179 - models.augmentation_factory - INFO - Epoch  40/10000: Train Loss: 0.450297, Val Loss: 0.402026, Weighted Loss: 0.435816 (Best✓)
2025-06-26 11:50:41,675 - models.augmentation_factory - INFO - Epoch  42/10000: Train Loss: 0.427855, Val Loss: 0.403144, Weighted Loss: 0.420442 (Best✓)
2025-06-26 11:50:42,177 - models.augmentation_factory - INFO - Epoch  44/10000: Train Loss: 0.382760, Val Loss: 0.296146, Weighted Loss: 0.356776 (Best✓)
2025-06-26 11:50:42,437 - models.augmentation_factory - INFO - Epoch  45/10000: Train Loss: 0.344195, Val Loss: 0.323670, Weighted Loss: 0.338037 (Best✓)
2025-06-26 11:50:42,945 - models.augmentation_factory - INFO - Epoch  47/10000: Train Loss: 0.276232, Val Loss: 0.387712, Weighted Loss: 0.309676 (Best✓)
2025-06-26 11:50:43,194 - models.augmentation_factory - INFO - Epoch  48/10000: Train Loss: 0.269347, Val Loss: 0.294446, Weighted Loss: 0.276877 (Best✓)
2025-06-26 11:50:43,441 - models.augmentation_factory - INFO - Epoch  49/10000: Train Loss: 0.274948, Val Loss: 0.223039, Weighted Loss: 0.259375 (Best✓)
2025-06-26 11:50:43,692 - models.augmentation_factory - INFO - Epoch  50/10000: Train Loss: 0.262796, Val Loss: 0.251838, Weighted Loss: 0.259508
2025-06-26 11:50:44,445 - models.augmentation_factory - INFO - Epoch  53/10000: Train Loss: 0.261773, Val Loss: 0.252138, Weighted Loss: 0.258882 (Best✓)
2025-06-26 11:50:44,943 - models.augmentation_factory - INFO - Epoch  55/10000: Train Loss: 0.230584, Val Loss: 0.201175, Weighted Loss: 0.221761 (Best✓)
2025-06-26 11:50:46,483 - models.augmentation_factory - INFO - Epoch  61/10000: Train Loss: 0.197146, Val Loss: 0.248917, Weighted Loss: 0.212677 (Best✓)
2025-06-26 11:50:46,732 - models.augmentation_factory - INFO - Epoch  62/10000: Train Loss: 0.170066, Val Loss: 0.190927, Weighted Loss: 0.176325 (Best✓)
2025-06-26 11:50:48,989 - models.augmentation_factory - INFO - Epoch  71/10000: Train Loss: 0.128913, Val Loss: 0.182431, Weighted Loss: 0.144968 (Best✓)
2025-06-26 11:50:54,647 - models.augmentation_factory - INFO - Epoch  93/10000: Train Loss: 0.135301, Val Loss: 0.150733, Weighted Loss: 0.139930 (Best✓)
2025-06-26 11:50:54,901 - models.augmentation_factory - INFO - Epoch  94/10000: Train Loss: 0.112517, Val Loss: 0.171419, Weighted Loss: 0.130188 (Best✓)
2025-06-26 11:50:56,198 - models.augmentation_factory - INFO - Epoch  99/10000: Train Loss: 0.094154, Val Loss: 0.111375, Weighted Loss: 0.099321 (Best✓)
2025-06-26 11:50:56,470 - models.augmentation_factory - INFO - Epoch 100/10000: Train Loss: 0.164764, Val Loss: 0.118357, Weighted Loss: 0.150842
2025-06-26 11:51:01,566 - models.augmentation_factory - INFO - Epoch 120/10000: Train Loss: 0.100575, Val Loss: 0.075452, Weighted Loss: 0.093038 (Best✓)
2025-06-26 11:51:09,031 - models.augmentation_factory - INFO - Epoch 150/10000: Train Loss: 0.164359, Val Loss: 0.242719, Weighted Loss: 0.187867
2025-06-26 11:51:21,591 - models.augmentation_factory - INFO - Epoch 200/10000: Train Loss: 0.147981, Val Loss: 0.129548, Weighted Loss: 0.142451
2025-06-26 11:51:34,411 - models.augmentation_factory - INFO - Epoch 250/10000: Train Loss: 0.205570, Val Loss: 0.124971, Weighted Loss: 0.181390
2025-06-26 11:51:47,282 - models.augmentation_factory - INFO - Epoch 300/10000: Train Loss: 0.138621, Val Loss: 0.277604, Weighted Loss: 0.180316
2025-06-26 11:51:59,802 - models.augmentation_factory - INFO - Epoch 350/10000: Train Loss: 0.265093, Val Loss: 0.212298, Weighted Loss: 0.249254
2025-06-26 11:52:12,431 - models.augmentation_factory - INFO - Epoch 400/10000: Train Loss: 0.125137, Val Loss: 0.095916, Weighted Loss: 0.116371
2025-06-26 11:52:25,025 - models.augmentation_factory - INFO - Epoch 450/10000: Train Loss: 0.099263, Val Loss: 0.215627, Weighted Loss: 0.134172
2025-06-26 11:52:27,532 - models.augmentation_factory - INFO - Epoch 460/10000: Train Loss: 0.083313, Val Loss: 0.111731, Weighted Loss: 0.091839 (Best✓)
2025-06-26 11:52:37,540 - models.augmentation_factory - INFO - Epoch 500/10000: Train Loss: 0.191465, Val Loss: 0.209138, Weighted Loss: 0.196767
2025-06-26 11:52:50,254 - models.augmentation_factory - INFO - Epoch 550/10000: Train Loss: 0.181059, Val Loss: 0.177121, Weighted Loss: 0.179878
