"""
结果管理器模块
负责保存实验结果、生成CSV文件、管理目录结构
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
import shutil

logger = logging.getLogger(__name__)


class ResultsManager:
    """实验结果管理器"""
    
    def __init__(self, config: Dict, dataset_name: str, timestamp: str):
        """
        初始化结果管理器
        
        Args:
            config: 配置字典
            dataset_name: 数据集名称
            timestamp: 时间戳
        """
        self.config = config
        self.dataset_name = dataset_name
        self.timestamp = timestamp
        
        # 创建结果目录结构
        self.base_results_dir = config['system']['save']['results_dir']
        self.dataset_results_dir = os.path.join(self.base_results_dir, dataset_name)
        self.experiment_results_dir = os.path.join(self.dataset_results_dir, timestamp)
        
        # 创建子目录
        self.individual_results_dir = os.path.join(self.experiment_results_dir, "individual_experiments")
        self.comparison_results_dir = os.path.join(self.experiment_results_dir, "comparison_summary")
        self.plots_data_dir = os.path.join(self.experiment_results_dir, "plots_data")
        self.configs_dir = os.path.join(self.experiment_results_dir, "configs")
        
        # 创建所有目录
        self._create_directories()
        
        logger.info(f"结果管理器初始化完成，结果目录: {self.experiment_results_dir}")
    
    def _create_directories(self):
        """创建所有必要的目录"""
        directories = [
            self.experiment_results_dir,
            self.individual_results_dir,
            self.comparison_results_dir,
            self.plots_data_dir,
            self.configs_dir
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def save_individual_experiment_results(self, experiment_index: int, config: Dict, 
                                         results: Dict) -> str:
        """
        保存单次实验结果
        
        Args:
            experiment_index: 实验索引
            config: 实验配置
            results: 实验结果
            
        Returns:
            保存的目录路径
        """
        # 创建单次实验目录
        exp_dir = os.path.join(self.individual_results_dir, f"experiment_{experiment_index:03d}")
        os.makedirs(exp_dir, exist_ok=True)
        
        # 保存配置文件
        config_path = os.path.join(exp_dir, "config.yaml")
        self._save_config(config, config_path)
        
        # 保存完整结果
        results_path = os.path.join(exp_dir, "results.json")
        self._save_json(results, results_path)
        
        # 保存性能指标CSV
        if 'performance_comparison' in results:
            self._save_performance_metrics_csv(results['performance_comparison'], exp_dir)
        
        # 保存训练曲线数据CSV
        if 'diffusion_results' in results:
            self._save_training_curves_csv(results['diffusion_results'], exp_dir, "diffusion")
        
        if 'classifier_results' in results:
            self._save_training_curves_csv(results['classifier_results'], exp_dir, "classifier")
        
        # 保存混淆矩阵CSV
        if 'evaluation_results' in results and 'confusion_matrix' in results['evaluation_results']:
            self._save_confusion_matrix_csv(results['evaluation_results']['confusion_matrix'], exp_dir)
        
        # 保存GAN指标CSV
        if 'gan_results' in results and results['gan_results']:
            self._save_gan_metrics_csv(results['gan_results'], exp_dir)
        
        logger.info(f"单次实验结果已保存: {exp_dir}")
        return exp_dir
    
    def _save_config(self, config: Dict, filepath: str):
        """保存配置文件"""
        import yaml
        with open(filepath, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    def _save_json(self, data: Dict, filepath: str):
        """保存JSON文件"""
        # 处理numpy数组
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        converted_data = convert_numpy(data)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, indent=2, ensure_ascii=False)
    
    def _save_performance_metrics_csv(self, performance_data: Dict, exp_dir: str):
        """保存性能指标CSV"""
        metrics_data = []
        
        # 基线指标
        baseline = performance_data['baseline_metrics']
        metrics_data.append({
            'Type': 'Baseline',
            'Accuracy': baseline['accuracy'],
            'Precision': baseline['precision'],
            'Recall': baseline['recall'],
            'F1_Score': baseline['f1_score']
        })
        
        # 增强指标
        augmented = performance_data['augmented_metrics']
        metrics_data.append({
            'Type': 'Augmented',
            'Accuracy': augmented['accuracy'],
            'Precision': augmented['precision'],
            'Recall': augmented['recall'],
            'F1_Score': augmented['f1_score']
        })
        
        # 改进指标
        improvement = performance_data['improvement']
        metrics_data.append({
            'Type': 'Improvement',
            'Accuracy': improvement['accuracy'],
            'Precision': improvement['precision'],
            'Recall': improvement['recall'],
            'F1_Score': improvement['f1_score']
        })
        
        df = pd.DataFrame(metrics_data)
        csv_path = os.path.join(exp_dir, "performance_metrics.csv")
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    
    def _save_training_curves_csv(self, training_data: Dict, exp_dir: str, model_type: str):
        """保存训练曲线数据CSV"""
        curves_data = []
        
        # 获取训练数据
        train_losses = training_data.get('train_losses', [])
        val_losses = training_data.get('val_losses', [])
        train_accs = training_data.get('train_accs', [])
        val_accs = training_data.get('val_accs', [])
        
        max_length = max(len(train_losses), len(val_losses), len(train_accs), len(val_accs))
        
        for i in range(max_length):
            row = {'Epoch': i + 1}
            
            if i < len(train_losses):
                row['Train_Loss'] = train_losses[i]
            if i < len(val_losses):
                row['Val_Loss'] = val_losses[i]
            if i < len(train_accs):
                row['Train_Accuracy'] = train_accs[i]
            if i < len(val_accs):
                row['Val_Accuracy'] = val_accs[i]
            
            curves_data.append(row)
        
        if curves_data:
            df = pd.DataFrame(curves_data)
            csv_path = os.path.join(exp_dir, f"{model_type}_training_curves.csv")
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    
    def _save_confusion_matrix_csv(self, confusion_matrix: np.ndarray, exp_dir: str):
        """保存混淆矩阵CSV"""
        df = pd.DataFrame(confusion_matrix)
        csv_path = os.path.join(exp_dir, "confusion_matrix.csv")
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    
    def _save_gan_metrics_csv(self, gan_data: Dict, exp_dir: str):
        """保存GAN指标CSV"""
        gan_metrics = []
        
        if 'gan_train' in gan_data:
            gan_metrics.append({
                'Metric': 'GAN_Train',
                'Score': gan_data['gan_train']
            })
        
        if 'gan_test' in gan_data:
            gan_metrics.append({
                'Metric': 'GAN_Test',
                'Score': gan_data['gan_test']
            })
        
        if gan_metrics:
            df = pd.DataFrame(gan_metrics)
            csv_path = os.path.join(exp_dir, "gan_metrics.csv")
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    
    def save_comparison_summary(self, results_summary: List[Dict]) -> str:
        """
        保存对比实验汇总
        
        Args:
            results_summary: 结果汇总列表
            
        Returns:
            汇总文件路径
        """
        if not results_summary:
            logger.warning("没有结果数据可保存")
            return ""
        
        # 转换为DataFrame
        df = pd.DataFrame(results_summary)
        
        # 保存汇总CSV
        summary_path = os.path.join(self.comparison_results_dir, "comparison_summary.csv")
        df.to_csv(summary_path, index=False, encoding='utf-8-sig')
        
        # 保存详细统计
        self._save_detailed_statistics(df)
        
        logger.info(f"对比实验汇总已保存: {summary_path}")
        return summary_path
    
    def _save_detailed_statistics(self, df: pd.DataFrame):
        """保存详细统计信息"""
        stats_data = {}
        
        # 基本统计
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        stats_data['basic_statistics'] = df[numeric_columns].describe().to_dict()
        
        # 最佳结果
        if 'augmented_accuracy' in df.columns:
            best_idx = df['augmented_accuracy'].idxmax()
            stats_data['best_result'] = df.loc[best_idx].to_dict()
        
        # 按方法分组统计
        if 'augmentation_method' in df.columns:
            method_stats = df.groupby('augmentation_method')[numeric_columns].mean().to_dict()
            stats_data['method_statistics'] = method_stats
        
        # 保存统计文件
        stats_path = os.path.join(self.comparison_results_dir, "detailed_statistics.json")
        self._save_json(stats_data, stats_path)
    
    def copy_config_to_results(self, config_path: str):
        """
        复制配置文件到结果目录
        
        Args:
            config_path: 原始配置文件路径
        """
        if os.path.exists(config_path):
            dest_path = os.path.join(self.configs_dir, "original_config.yaml")
            shutil.copy2(config_path, dest_path)
            logger.info(f"原始配置文件已复制到结果目录: {dest_path}")
    
    def get_experiment_summary(self) -> Dict:
        """
        获取实验汇总信息
        
        Returns:
            实验汇总字典
        """
        return {
            'dataset_name': self.dataset_name,
            'timestamp': self.timestamp,
            'results_directory': self.experiment_results_dir,
            'individual_experiments_dir': self.individual_results_dir,
            'comparison_summary_dir': self.comparison_results_dir,
            'plots_data_dir': self.plots_data_dir,
            'configs_dir': self.configs_dir
        }
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        temp_dirs = ['temp_configs']
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                logger.info(f"临时目录已清理: {temp_dir}")
