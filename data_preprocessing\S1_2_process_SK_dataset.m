% SK数据集预处理程序
% 处理SK轴承故障数据集，数据分割并保存为MATLAB和NPY格式
% 将每个4096点的样本分成4个1024点样本，增加样本数量
% 原样本包括三种状态：正常(1-40组)、故障预警(81-120组)、故障(211-250组)

% 清空工作区和命令窗口
clear;
clc;

try
    % 开始计时
    tic;
    
    % 调用处理函数
    process_SK_dataset();
    
    % 结束计时
    elapsed_time = toc;
    fprintf('处理完成! 耗时: %.2f 秒\n', elapsed_time);
catch e
    % 捕获并显示错误
    fprintf('处理过程中发生错误:\n');
    disp(e.message);
    if ~isempty(e.stack)
        fprintf('错误位置: %s (行 %d)\n', e.stack(1).name, e.stack(1).line);
    end
end

% ================== 主处理函数 ==================
function process_SK_dataset()
    % ==================== 参数设置 ====================
    sample_length = 1024;         % 每个样本的长度（采样点数）
    train_ratio = 0.7;            % 训练集占总数据的比例
    random_seed = 42;             % 随机种子，确保结果可重现
    
    % 样本索引设置
    normal_indices = 1:40;        % 正常样本索引范围
    warning_indices = 81:120;     % 故障预警样本索引范围
    fault_indices = 211:250;      % 故障样本索引范围
    
    % ==================== 路径设置 ====================
    % 获取脚本所在目录的上级目录作为基础路径
    script_dir = fileparts(mfilename('fullpath'));
    base_dir = fileparts(script_dir);  % 上级目录
    
    % 使用基础路径构建完整路径
    ori_path = fullfile(base_dir, 'dataset', 'SK', 'ori_mat');      % 原始数据目录
    final_mat_path = fullfile(base_dir, 'dataset', 'SK', 'used_mat');  % 最终mat文件保存目录
    final_npy_path = fullfile(base_dir, 'dataset', 'SK', 'used_npy');  % 最终npy文件保存目录
    
    fprintf('使用以下路径:\n');
    fprintf('原始数据目录: %s\n', ori_path);
    fprintf('Mat文件保存目录: %s\n', final_mat_path);
    fprintf('NPY文件保存目录: %s\n', final_npy_path);
    
    % 检查原始数据目录是否存在
    if ~exist(ori_path, 'dir')
        error('原始数据目录不存在: %s', ori_path);
    end
    
    fprintf('开始处理SK数据集...\n');
    
    % ==================== 创建保存目录 ====================
    if ~exist(fullfile(final_mat_path, 'train'), 'dir')
        mkdir(fullfile(final_mat_path, 'train'));
    end
    if ~exist(fullfile(final_mat_path, 'test'), 'dir')
        mkdir(fullfile(final_mat_path, 'test'));
    end
    if ~exist(fullfile(final_npy_path, 'train'), 'dir')
        mkdir(fullfile(final_npy_path, 'train'));
    end
    if ~exist(fullfile(final_npy_path, 'test'), 'dir')
        mkdir(fullfile(final_npy_path, 'test'));
    end
    
    % ==================== 读取原始数据 ====================
    fprintf('读取原始数据文件...\n');
    
    % 找到原始mat文件
    mat_files = dir(fullfile(ori_path, '*.mat'));
    if isempty(mat_files)
        % 如果没有找到mat文件，提供更详细的错误信息
        dir_contents = dir(ori_path);
        fprintf('目录 %s 中的内容:\n', ori_path);
        for i = 1:length(dir_contents)
            item = dir_contents(i);
            fprintf('  %s  %s  %d bytes\n', item.name, datestr(item.datenum), item.bytes);
        end
        error(['未在 %s 目录下找到mat文件。\n请确保:\n' ...
               '1. 路径正确\n' ...
               '2. 文件扩展名为.mat\n' ...
               '3. 有权限访问该目录'], ori_path);
    end
    
    fprintf('找到以下mat文件:\n');
    for i = 1:length(mat_files)
        fprintf('  %s (%d bytes)\n', mat_files(i).name, mat_files(i).bytes);
    end
    
    % 读取第一个mat文件
    mat_file_path = fullfile(ori_path, mat_files(1).name);
    fprintf('读取文件: %s\n', mat_file_path);
    try
        original_data = load(mat_file_path);
    catch e
        fprintf('读取文件 %s 时出错:\n', mat_file_path);
        rethrow(e);
    end
    
    % 获取数据字段名（mat文件中的变量名可能不确定）
    field_names = fieldnames(original_data);
    if isempty(field_names)
        error('mat文件中未找到数据');
    end
    
    % 获取原始波形数据
    signals = original_data.(field_names{1});
    fprintf('成功读取%d组原始波形\n', size(signals, 1));
    
    % ==================== 创建可视化窗口 ====================
    figure('Name', 'SK数据集样本示例', 'Position', [100, 100, 1200, 800]);
    
    % ==================== 处理每个类别的数据 ====================
    all_data = [];
    all_labels = [];
    
    % 处理正常样本 (标签0)
    fprintf('处理正常样本 (标签0)...\n');
    normal_samples = process_class_samples(signals(normal_indices, :), 0, sample_length);
    
    % 可视化部分样本
    subplot(3, 1, 1);
    plot(normal_samples(1, :));
    title('正常样本示例 (标签0)');
    
    % 处理故障预警样本 (标签1)
    fprintf('处理故障预警样本 (标签1)...\n');
    warning_samples = process_class_samples(signals(warning_indices, :), 1, sample_length);
    
    % 可视化部分样本
    subplot(3, 1, 2);
    plot(warning_samples(1, :));
    title('故障预警样本示例 (标签1)');
    
    % 处理故障样本 (标签2)
    fprintf('处理故障样本 (标签2)...\n');
    fault_samples = process_class_samples(signals(fault_indices, :), 2, sample_length);
    
    % 可视化部分样本
    subplot(3, 1, 3);
    plot(fault_samples(1, :));
    title('故障样本示例 (标签2)');
    
    % 保存样本可视化图像
    saveas(gcf, fullfile(final_mat_path, 'samples_visualization.png'));
    
    % 合并所有类别的样本
    all_samples = [normal_samples; warning_samples; fault_samples];
    all_labels = [zeros(size(normal_samples, 1), 1); 
                 ones(size(warning_samples, 1), 1); 
                 2*ones(size(fault_samples, 1), 1)];
    
    fprintf('总共生成 %d 个样本\n', size(all_samples, 1));
    
    % ==================== 划分训练集和测试集 ====================
    fprintf('划分训练集和测试集 (比例 %.1f/%.1f)...\n', train_ratio, 1-train_ratio);
    
    % 设置随机种子以确保结果可重现
    rng(random_seed);
    
    % 随机打乱样本顺序
    shuffle_idx = randperm(size(all_samples, 1));
    all_samples = all_samples(shuffle_idx, :);
    all_labels = all_labels(shuffle_idx);
    
    % 根据比例划分训练集和测试集
    train_size = round(size(all_samples, 1) * train_ratio);
    
    % 使用与MFPT相同的变量名
    all_train_data = all_samples(1:train_size, :);
    all_train_labels = int32(all_labels(1:train_size));  % 转换为int32类型以与MFPT保持一致
    
    all_test_data = all_samples(train_size+1:end, :);
    all_test_labels = int32(all_labels(train_size+1:end));  % 转换为int32类型以与MFPT保持一致
    
    fprintf('训练集: %d 个样本\n', size(all_train_data, 1));
    fprintf('测试集: %d 个样本\n', size(all_test_data, 1));
    
    % ==================== 保存为MATLAB格式 ====================
    fprintf('保存为MATLAB格式...\n');
    
    % 保存训练集，使用与MFPT相同的变量名
    save(fullfile(final_mat_path, 'train', 'train_dataset.mat'), 'all_train_data', 'all_train_labels');
    
    % 保存测试集，使用与MFPT相同的变量名
    save(fullfile(final_mat_path, 'test', 'test_dataset.mat'), 'all_test_data', 'all_test_labels');
    
    % ==================== 生成并执行Python脚本来转换为NPY格式 ====================
    fprintf('生成并执行Python脚本来转换为NPY格式...\n');
    
    % 创建临时Python脚本文件
    temp_dir = fullfile(tempdir, 'sk_temp');
    if ~exist(temp_dir, 'dir')
        mkdir(temp_dir);
    end
    
    py_script_path = fullfile(temp_dir, 'convert_to_numpy.py');
    fid = fopen(py_script_path, 'w');
    
    if fid < 0
        error('无法创建Python转换脚本: %s', py_script_path);
    end
    
    % 写入Python脚本内容
    fprintf(fid, '#!/usr/bin/env python\n');
    fprintf(fid, '# -*- coding: utf-8 -*-\n\n');
    fprintf(fid, '# 此脚本由MATLAB自动生成，用于将SK数据集从MAT格式转换为NPY格式\n\n');
    
    fprintf(fid, 'import numpy as np\n');
    fprintf(fid, 'import scipy.io as sio\n');
    fprintf(fid, 'import os\n\n');
    
    fprintf(fid, '# 创建输出目录\n');
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n', strrep(fullfile(final_npy_path, 'train'), '\', '\\'));
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n\n', strrep(fullfile(final_npy_path, 'test'), '\', '\\'));
    
    fprintf(fid, '# 加载训练数据\n');
    fprintf(fid, 'print("加载训练数据...")\n');
    fprintf(fid, 'train_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'train', 'train_dataset.mat'), '\', '\\'));
    
    fprintf(fid, '# 加载测试数据\n');
    fprintf(fid, 'print("加载测试数据...")\n');
    fprintf(fid, 'test_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'test', 'test_dataset.mat'), '\', '\\'));
    
    fprintf(fid, '# 保存训练数据\n');
    fprintf(fid, 'print("保存训练数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", train_data["all_train_data"])\n', strrep(fullfile(final_npy_path, 'train', 'sk_data.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", train_data["all_train_labels"])\n\n', strrep(fullfile(final_npy_path, 'train', 'sk_label.npy'), '\', '\\'));
    
    fprintf(fid, '# 保存测试数据\n');
    fprintf(fid, 'print("保存测试数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", test_data["all_test_data"])\n', strrep(fullfile(final_npy_path, 'test', 'sk_data.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", test_data["all_test_labels"])\n\n', strrep(fullfile(final_npy_path, 'test', 'sk_label.npy'), '\', '\\'));
    
    fprintf(fid, 'print("转换完成！")\n');
    fclose(fid);
    
    % 创建说明文件
    readme_path = fullfile(final_npy_path, 'README.txt');
    fid = fopen(readme_path, 'w');
    if fid > 0
        fprintf(fid, 'SK数据集 - NPY格式数据说明\n');
        fprintf(fid, '==============================\n\n');
        fprintf(fid, '数据格式说明:\n');
        fprintf(fid, '- NPY文件格式: \n');
        fprintf(fid, '  * 训练数据: sk_data.npy (样本矩阵), sk_label.npy (整数类型标签)\n');
        fprintf(fid, '  * 测试数据: sk_data.npy (样本矩阵), sk_label.npy (整数类型标签)\n\n');
        fprintf(fid, '标签说明:\n');
        fprintf(fid, '- 0: 正常\n');
        fprintf(fid, '- 1: 故障预警\n');
        fprintf(fid, '- 2: 故障\n\n');
        fprintf(fid, '样本数量:\n');
        fprintf(fid, '- 训练集: %d 个样本\n', size(all_train_data, 1));
        fprintf(fid, '- 测试集: %d 个样本\n\n', size(all_test_data, 1));
        fprintf(fid, '采样方法:\n');
        fprintf(fid, '- 从每个类别的原始信号中随机提取样本，允许样本重叠\n');
        fprintf(fid, '- 每个样本包含 %d 个数据点\n', sample_length);
        fclose(fid);
    end
    
    % 执行Python脚本
    fprintf('正在执行Python脚本以生成NPY格式数据文件...\n');
    
    try
        % 判断系统平台
        if ispc
            % Windows系统
            [status, cmdout] = system(['python "', py_script_path, '"']);
        else
            % Linux/Mac系统
            [status, cmdout] = system(['python3 "', py_script_path, '"']);
        end
        
        if status == 0
            fprintf('Python脚本执行成功！\n%s\n', cmdout);
        else
            fprintf('Python脚本执行失败！错误码: %d\n%s\n', status, cmdout);
            fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
            fprintf('python "%s"\n', py_script_path);
        end
    catch e
        fprintf('执行Python脚本时出错: %s\n', e.message);
        fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
        fprintf('python "%s"\n', py_script_path);
    end
    
    % 尝试删除临时Python脚本
    try
        delete(py_script_path);
        rmdir(temp_dir);
        fprintf('临时Python脚本已删除\n');
    catch
        fprintf('无法删除临时Python脚本: %s\n', py_script_path);
    end
    
    fprintf('SK数据集预处理完成!\n');
end

function samples = process_class_samples(class_signals, label, sample_length)
    % 将一个类别的所有原始信号处理成样本
    % 输入:
    %   class_signals: 原始信号，每行一个波形
    %   label: 类别标签
    %   sample_length: 每个样本的长度
    % 输出:
    %   samples: 处理后的样本矩阵，每行一个样本
    
    num_signals = size(class_signals, 1);
    samples = [];
    
    for i = 1:num_signals
        signal = class_signals(i, :);
        
        % 每个4096点的信号分割成4个1024点的子样本
        for j = 1:4
            start_idx = (j-1)*sample_length + 1;
            end_idx = j*sample_length;
            
            % 截取子样本
            sub_sample = signal(start_idx:end_idx);
            
            % 添加到样本集
            samples = [samples; sub_sample];
        end
    end
end 