2025-06-26 12:09:47,561 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\test_combined_dataset_20250626_120947.log
2025-06-26 12:09:47,588 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 12:09:47,588 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 12:09:47,595 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 12:09:47,604 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 12:09:47,604 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 12:09:47,605 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 12:09:47,605 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 12:09:47,605 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 12:09:47,605 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 12:09:47,605 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:09:47,605 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:09:47,605 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:09:47,605 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:09:47,606 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:09:47,606 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:09:47,606 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 12:09:47,606 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 12:09:47,611 - common.data_loader - INFO - 数据归一化完成，方法: minmax
