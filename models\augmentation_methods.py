"""
数据增强方法模块
实现多种数据增强方法：CGAN, WGAN, WGAN-GP, DDPM, DCGAN等
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from abc import ABC, abstractmethod
from typing import Tuple, Dict, Optional
import logging

logger = logging.getLogger(__name__)


class BaseAugmentationMethod(ABC):
    """数据增强方法基类"""
    
    def __init__(self, config: Dict, device: torch.device):
        self.config = config
        self.device = device
        self.method_name = self.__class__.__name__
        
    @abstractmethod
    def train(self, train_loader, val_loader=None) -> Dict:
        """训练模型"""
        pass
    
    @abstractmethod
    def generate_samples(self, num_samples_per_class: int, signal_length: int, 
                        class_labels: Optional[torch.Tensor] = None) -> Tuple[np.ndarray, np.ndarray]:
        """生成样本"""
        pass
    
    @abstractmethod
    def save_model(self, save_path: str):
        """保存模型"""
        pass
    
    @abstractmethod
    def load_model(self, load_path: str):
        """加载模型"""
        pass


class Generator1D(nn.Module):
    """一维信号生成器（用于GAN系列）"""
    
    def __init__(self, latent_dim: int, signal_length: int, num_classes: int = 0):
        super().__init__()
        self.latent_dim = latent_dim
        self.signal_length = signal_length
        self.num_classes = num_classes
        
        # 计算初始特征图大小
        self.init_size = signal_length // 16  # 经过4次上采样
        
        # 标签嵌入（如果是条件生成）
        if num_classes > 0:
            self.label_emb = nn.Embedding(num_classes, latent_dim)
        
        # 主网络
        self.fc = nn.Linear(latent_dim, 128 * self.init_size)
        
        self.conv_blocks = nn.Sequential(
            nn.BatchNorm1d(128),
            nn.Upsample(scale_factor=2),
            nn.Conv1d(128, 128, 3, stride=1, padding=1),
            nn.BatchNorm1d(128),
            nn.LeakyReLU(0.2, inplace=True),
            
            nn.Upsample(scale_factor=2),
            nn.Conv1d(128, 64, 3, stride=1, padding=1),
            nn.BatchNorm1d(64),
            nn.LeakyReLU(0.2, inplace=True),
            
            nn.Upsample(scale_factor=2),
            nn.Conv1d(64, 32, 3, stride=1, padding=1),
            nn.BatchNorm1d(32),
            nn.LeakyReLU(0.2, inplace=True),
            
            nn.Upsample(scale_factor=2),
            nn.Conv1d(32, 1, 3, stride=1, padding=1),
            nn.Tanh()
        )
    
    def forward(self, noise, labels=None):
        if self.num_classes > 0 and labels is not None:
            # 条件生成
            label_emb = self.label_emb(labels)
            gen_input = torch.mul(label_emb, noise)
        else:
            gen_input = noise
        
        out = self.fc(gen_input)
        out = out.view(out.shape[0], 128, self.init_size)
        signal = self.conv_blocks(out)
        
        # 调整到目标长度
        if signal.size(-1) != self.signal_length:
            signal = nn.functional.interpolate(signal, size=self.signal_length, mode='linear', align_corners=False)
        
        return signal


class Discriminator1D(nn.Module):
    """一维信号判别器（用于GAN系列）"""
    
    def __init__(self, signal_length: int, num_classes: int = 0):
        super().__init__()
        self.signal_length = signal_length
        self.num_classes = num_classes
        
        # 标签嵌入（如果是条件判别）
        if num_classes > 0:
            self.label_emb = nn.Embedding(num_classes, signal_length)
        
        # 主网络
        self.conv_blocks = nn.Sequential(
            nn.Conv1d(1 if num_classes == 0 else 2, 32, 3, stride=2, padding=1),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.25),
            
            nn.Conv1d(32, 64, 3, stride=2, padding=1),
            nn.BatchNorm1d(64),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.25),
            
            nn.Conv1d(64, 128, 3, stride=2, padding=1),
            nn.BatchNorm1d(128),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.25),
            
            nn.Conv1d(128, 256, 3, stride=2, padding=1),
            nn.BatchNorm1d(256),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.25),
        )
        
        # 计算展平后的大小
        self.adv_layer = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
    
    def forward(self, signal, labels=None):
        if self.num_classes > 0 and labels is not None:
            # 条件判别
            label_emb = self.label_emb(labels).unsqueeze(1)
            d_in = torch.cat((signal, label_emb), dim=1)
        else:
            d_in = signal
        
        features = self.conv_blocks(d_in)
        validity = self.adv_layer(features)
        
        return validity


class CGAN(BaseAugmentationMethod):
    """条件生成对抗网络"""
    
    def __init__(self, config: Dict, device: torch.device):
        super().__init__(config, device)
        
        self.signal_length = config['dataset']['data_loading']['signal_length']
        self.num_classes = config['dataset']['datasets'][config['dataset']['name']]['num_classes']
        
        # 获取CGAN配置
        cgan_config = config['augmentation']['cgan']
        self.latent_dim = cgan_config['latent_dim']
        self.generator_lr = cgan_config['generator_lr']
        self.discriminator_lr = cgan_config['discriminator_lr']
        self.beta1 = cgan_config['beta1']
        self.epochs = cgan_config['epochs']
        
        # 创建模型
        self.generator = Generator1D(self.latent_dim, self.signal_length, self.num_classes).to(device)
        self.discriminator = Discriminator1D(self.signal_length, self.num_classes).to(device)
        
        # 损失函数
        self.adversarial_loss = nn.BCELoss()
        
        # 优化器
        self.optimizer_G = optim.Adam(self.generator.parameters(), lr=self.generator_lr, betas=(self.beta1, 0.999))
        self.optimizer_D = optim.Adam(self.discriminator.parameters(), lr=self.discriminator_lr, betas=(self.beta1, 0.999))
        
        logger.info(f"CGAN初始化完成，潜在维度: {self.latent_dim}, 类别数: {self.num_classes}")
    
    def train(self, train_loader, val_loader=None) -> Dict:
        """训练CGAN"""
        logger.info("开始训练CGAN...")
        
        # 训练历史
        g_losses = []
        d_losses = []
        
        for epoch in range(self.epochs):
            epoch_g_loss = 0.0
            epoch_d_loss = 0.0
            num_batches = 0
            
            for i, (real_signals, labels) in enumerate(train_loader):
                batch_size = real_signals.size(0)
                real_signals = real_signals.to(self.device)
                labels = labels.to(self.device)
                
                # 确保信号有正确的维度
                if real_signals.dim() == 2:
                    real_signals = real_signals.unsqueeze(1)
                
                # 真实和虚假标签
                valid = torch.ones(batch_size, 1, device=self.device)
                fake = torch.zeros(batch_size, 1, device=self.device)
                
                # ---------------------
                #  训练生成器
                # ---------------------
                self.optimizer_G.zero_grad()
                
                # 生成噪声和标签
                z = torch.randn(batch_size, self.latent_dim, device=self.device)
                gen_labels = torch.randint(0, self.num_classes, (batch_size,), device=self.device)
                
                # 生成信号
                gen_signals = self.generator(z, gen_labels)
                
                # 生成器损失
                validity = self.discriminator(gen_signals, gen_labels)
                g_loss = self.adversarial_loss(validity, valid)
                
                g_loss.backward()
                self.optimizer_G.step()
                
                # ---------------------
                #  训练判别器
                # ---------------------
                self.optimizer_D.zero_grad()
                
                # 真实信号损失
                real_validity = self.discriminator(real_signals, labels)
                d_real_loss = self.adversarial_loss(real_validity, valid)
                
                # 虚假信号损失
                fake_validity = self.discriminator(gen_signals.detach(), gen_labels)
                d_fake_loss = self.adversarial_loss(fake_validity, fake)
                
                # 总判别器损失
                d_loss = (d_real_loss + d_fake_loss) / 2
                
                d_loss.backward()
                self.optimizer_D.step()
                
                # 累计损失
                epoch_g_loss += g_loss.item()
                epoch_d_loss += d_loss.item()
                num_batches += 1
                
                if i % 100 == 0:
                    logger.info(f"Epoch {epoch+1}/{self.epochs}, Batch {i}/{len(train_loader)}, "
                              f"G_loss: {g_loss.item():.4f}, D_loss: {d_loss.item():.4f}")
            
            # 记录平均损失
            avg_g_loss = epoch_g_loss / num_batches
            avg_d_loss = epoch_d_loss / num_batches
            g_losses.append(avg_g_loss)
            d_losses.append(avg_d_loss)
            
            if (epoch + 1) % 100 == 0:
                logger.info(f"Epoch {epoch+1}/{self.epochs} 完成, "
                          f"平均G_loss: {avg_g_loss:.4f}, 平均D_loss: {avg_d_loss:.4f}")
        
        logger.info("CGAN训练完成")
        
        return {
            'generator_losses': g_losses,
            'discriminator_losses': d_losses,
            'total_epochs': self.epochs
        }
    
    def generate_samples(self, num_samples_per_class: int, signal_length: int, 
                        class_labels: Optional[torch.Tensor] = None) -> Tuple[np.ndarray, np.ndarray]:
        """生成样本"""
        self.generator.eval()
        
        all_generated_data = []
        all_generated_labels = []
        
        with torch.no_grad():
            for class_idx in range(self.num_classes):
                # 生成噪声
                z = torch.randn(num_samples_per_class, self.latent_dim, device=self.device)
                
                # 创建类别标签
                labels = torch.full((num_samples_per_class,), class_idx, device=self.device, dtype=torch.long)
                
                # 生成样本
                generated_samples = self.generator(z, labels)
                
                # 转换为numpy
                generated_samples = generated_samples.squeeze(1).cpu().numpy()
                
                all_generated_data.append(generated_samples)
                all_generated_labels.extend([class_idx] * num_samples_per_class)
        
        generated_data = np.concatenate(all_generated_data, axis=0)
        generated_labels = np.array(all_generated_labels)
        
        return generated_data, generated_labels
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'generator_state_dict': self.generator.state_dict(),
            'discriminator_state_dict': self.discriminator.state_dict(),
            'optimizer_G_state_dict': self.optimizer_G.state_dict(),
            'optimizer_D_state_dict': self.optimizer_D.state_dict(),
            'config': self.config
        }, save_path)
    
    def load_model(self, load_path: str):
        """加载模型"""
        checkpoint = torch.load(load_path, map_location=self.device)
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
        self.optimizer_G.load_state_dict(checkpoint['optimizer_G_state_dict'])
        self.optimizer_D.load_state_dict(checkpoint['optimizer_D_state_dict'])


class WGAN(BaseAugmentationMethod):
    """Wasserstein GAN"""

    def __init__(self, config: Dict, device: torch.device):
        super().__init__(config, device)

        self.signal_length = config['dataset']['data_loading']['signal_length']
        self.num_classes = config['dataset']['datasets'][config['dataset']['name']]['num_classes']

        # 获取WGAN配置
        wgan_config = config['augmentation']['wgan']
        self.latent_dim = wgan_config['latent_dim']
        self.generator_lr = wgan_config['generator_lr']
        self.critic_lr = wgan_config['critic_lr']
        self.n_critic = wgan_config['n_critic']
        self.clip_value = wgan_config['clip_value']
        self.epochs = wgan_config['epochs']

        # 创建模型
        self.generator = Generator1D(self.latent_dim, self.signal_length, self.num_classes).to(device)
        self.critic = Discriminator1D(self.signal_length, self.num_classes).to(device)

        # 移除critic最后的sigmoid层（WGAN不需要）
        self.critic.adv_layer = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(256, 1)
        ).to(device)

        # 优化器
        self.optimizer_G = optim.RMSprop(self.generator.parameters(), lr=self.generator_lr)
        self.optimizer_C = optim.RMSprop(self.critic.parameters(), lr=self.critic_lr)

        logger.info(f"WGAN初始化完成，潜在维度: {self.latent_dim}, 类别数: {self.num_classes}")

    def train(self, train_loader, val_loader=None) -> Dict:
        """训练WGAN"""
        logger.info("开始训练WGAN...")

        # 训练历史
        g_losses = []
        c_losses = []

        for epoch in range(self.epochs):
            epoch_g_loss = 0.0
            epoch_c_loss = 0.0
            num_batches = 0

            for i, (real_signals, labels) in enumerate(train_loader):
                batch_size = real_signals.size(0)
                real_signals = real_signals.to(self.device)
                labels = labels.to(self.device)

                # 确保信号有正确的维度
                if real_signals.dim() == 2:
                    real_signals = real_signals.unsqueeze(1)

                # ---------------------
                #  训练Critic
                # ---------------------
                for _ in range(self.n_critic):
                    self.optimizer_C.zero_grad()

                    # 生成虚假样本
                    z = torch.randn(batch_size, self.latent_dim, device=self.device)
                    gen_labels = torch.randint(0, self.num_classes, (batch_size,), device=self.device)
                    fake_signals = self.generator(z, gen_labels).detach()

                    # Critic损失
                    real_validity = self.critic(real_signals, labels)
                    fake_validity = self.critic(fake_signals, gen_labels)
                    c_loss = -torch.mean(real_validity) + torch.mean(fake_validity)

                    c_loss.backward()
                    self.optimizer_C.step()

                    # 权重裁剪
                    for p in self.critic.parameters():
                        p.data.clamp_(-self.clip_value, self.clip_value)

                # ---------------------
                #  训练生成器
                # ---------------------
                self.optimizer_G.zero_grad()

                # 生成样本
                z = torch.randn(batch_size, self.latent_dim, device=self.device)
                gen_labels = torch.randint(0, self.num_classes, (batch_size,), device=self.device)
                gen_signals = self.generator(z, gen_labels)

                # 生成器损失
                fake_validity = self.critic(gen_signals, gen_labels)
                g_loss = -torch.mean(fake_validity)

                g_loss.backward()
                self.optimizer_G.step()

                # 累计损失
                epoch_g_loss += g_loss.item()
                epoch_c_loss += c_loss.item()
                num_batches += 1

                if i % 100 == 0:
                    logger.info(f"Epoch {epoch+1}/{self.epochs}, Batch {i}/{len(train_loader)}, "
                              f"G_loss: {g_loss.item():.4f}, C_loss: {c_loss.item():.4f}")

            # 记录平均损失
            avg_g_loss = epoch_g_loss / num_batches
            avg_c_loss = epoch_c_loss / num_batches
            g_losses.append(avg_g_loss)
            c_losses.append(avg_c_loss)

            if (epoch + 1) % 100 == 0:
                logger.info(f"Epoch {epoch+1}/{self.epochs} 完成, "
                          f"平均G_loss: {avg_g_loss:.4f}, 平均C_loss: {avg_c_loss:.4f}")

        logger.info("WGAN训练完成")

        return {
            'generator_losses': g_losses,
            'critic_losses': c_losses,
            'total_epochs': self.epochs
        }

    def generate_samples(self, num_samples_per_class: int, signal_length: int,
                        class_labels: Optional[torch.Tensor] = None) -> Tuple[np.ndarray, np.ndarray]:
        """生成样本"""
        self.generator.eval()

        all_generated_data = []
        all_generated_labels = []

        with torch.no_grad():
            for class_idx in range(self.num_classes):
                # 生成噪声
                z = torch.randn(num_samples_per_class, self.latent_dim, device=self.device)

                # 创建类别标签
                labels = torch.full((num_samples_per_class,), class_idx, device=self.device, dtype=torch.long)

                # 生成样本
                generated_samples = self.generator(z, labels)

                # 转换为numpy
                generated_samples = generated_samples.squeeze(1).cpu().numpy()

                all_generated_data.append(generated_samples)
                all_generated_labels.extend([class_idx] * num_samples_per_class)

        generated_data = np.concatenate(all_generated_data, axis=0)
        generated_labels = np.array(all_generated_labels)

        return generated_data, generated_labels

    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'generator_state_dict': self.generator.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'optimizer_G_state_dict': self.optimizer_G.state_dict(),
            'optimizer_C_state_dict': self.optimizer_C.state_dict(),
            'config': self.config
        }, save_path)

    def load_model(self, load_path: str):
        """加载模型"""
        checkpoint = torch.load(load_path, map_location=self.device)
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.optimizer_G.load_state_dict(checkpoint['optimizer_G_state_dict'])
        self.optimizer_C.load_state_dict(checkpoint['optimizer_C_state_dict'])


class WGAN_GP(BaseAugmentationMethod):
    """WGAN with Gradient Penalty"""

    def __init__(self, config: Dict, device: torch.device):
        super().__init__(config, device)

        self.signal_length = config['dataset']['data_loading']['signal_length']
        self.num_classes = config['dataset']['datasets'][config['dataset']['name']]['num_classes']

        # 获取WGAN-GP配置
        wgan_gp_config = config['augmentation']['wgan_gp']
        self.latent_dim = wgan_gp_config['latent_dim']
        self.generator_lr = wgan_gp_config['generator_lr']
        self.critic_lr = wgan_gp_config['critic_lr']
        self.n_critic = wgan_gp_config['n_critic']
        self.lambda_gp = wgan_gp_config['lambda_gp']
        self.epochs = wgan_gp_config['epochs']

        # 创建模型
        self.generator = Generator1D(self.latent_dim, self.signal_length, self.num_classes).to(device)
        self.critic = Discriminator1D(self.signal_length, self.num_classes).to(device)

        # 移除critic最后的sigmoid层
        self.critic.adv_layer = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(256, 1)
        ).to(device)

        # 优化器
        self.optimizer_G = optim.Adam(self.generator.parameters(), lr=self.generator_lr, betas=(0.0, 0.9))
        self.optimizer_C = optim.Adam(self.critic.parameters(), lr=self.critic_lr, betas=(0.0, 0.9))

        logger.info(f"WGAN-GP初始化完成，潜在维度: {self.latent_dim}, 类别数: {self.num_classes}")

    def compute_gradient_penalty(self, real_samples, fake_samples, labels):
        """计算梯度惩罚"""
        batch_size = real_samples.size(0)

        # 随机插值
        alpha = torch.rand(batch_size, 1, 1, device=self.device)
        interpolates = alpha * real_samples + (1 - alpha) * fake_samples
        interpolates.requires_grad_(True)

        # 计算判别器输出
        d_interpolates = self.critic(interpolates, labels)

        # 计算梯度
        gradients = torch.autograd.grad(
            outputs=d_interpolates,
            inputs=interpolates,
            grad_outputs=torch.ones_like(d_interpolates),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]

        # 计算梯度惩罚
        gradients = gradients.view(batch_size, -1)
        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()

        return gradient_penalty

    def train(self, train_loader, val_loader=None) -> Dict:
        """训练WGAN-GP"""
        logger.info("开始训练WGAN-GP...")

        # 训练历史
        g_losses = []
        c_losses = []

        for epoch in range(self.epochs):
            epoch_g_loss = 0.0
            epoch_c_loss = 0.0
            num_batches = 0

            for i, (real_signals, labels) in enumerate(train_loader):
                batch_size = real_signals.size(0)
                real_signals = real_signals.to(self.device)
                labels = labels.to(self.device)

                # 确保信号有正确的维度
                if real_signals.dim() == 2:
                    real_signals = real_signals.unsqueeze(1)

                # ---------------------
                #  训练Critic
                # ---------------------
                for _ in range(self.n_critic):
                    self.optimizer_C.zero_grad()

                    # 生成虚假样本
                    z = torch.randn(batch_size, self.latent_dim, device=self.device)
                    gen_labels = torch.randint(0, self.num_classes, (batch_size,), device=self.device)
                    fake_signals = self.generator(z, gen_labels).detach()

                    # Critic损失
                    real_validity = self.critic(real_signals, labels)
                    fake_validity = self.critic(fake_signals, gen_labels)

                    # 梯度惩罚
                    gradient_penalty = self.compute_gradient_penalty(real_signals, fake_signals, labels)

                    # 总损失
                    c_loss = -torch.mean(real_validity) + torch.mean(fake_validity) + self.lambda_gp * gradient_penalty

                    c_loss.backward()
                    self.optimizer_C.step()

                # ---------------------
                #  训练生成器
                # ---------------------
                self.optimizer_G.zero_grad()

                # 生成样本
                z = torch.randn(batch_size, self.latent_dim, device=self.device)
                gen_labels = torch.randint(0, self.num_classes, (batch_size,), device=self.device)
                gen_signals = self.generator(z, gen_labels)

                # 生成器损失
                fake_validity = self.critic(gen_signals, gen_labels)
                g_loss = -torch.mean(fake_validity)

                g_loss.backward()
                self.optimizer_G.step()

                # 累计损失
                epoch_g_loss += g_loss.item()
                epoch_c_loss += c_loss.item()
                num_batches += 1

            # 记录平均损失
            avg_g_loss = epoch_g_loss / num_batches
            avg_c_loss = epoch_c_loss / num_batches
            g_losses.append(avg_g_loss)
            c_losses.append(avg_c_loss)

            logger.info(f"Epoch {epoch+1}/{self.epochs}: G_loss: {avg_g_loss:.4f}, C_loss: {avg_c_loss:.4f}")

        logger.info("WGAN-GP训练完成")

        return {
            'generator_losses': g_losses,
            'critic_losses': c_losses,
            'total_epochs': self.epochs
        }

    def generate_samples(self, num_samples_per_class: int, signal_length: int,
                        class_labels: Optional[torch.Tensor] = None) -> Tuple[np.ndarray, np.ndarray]:
        """生成样本"""
        self.generator.eval()

        all_generated_data = []
        all_generated_labels = []

        with torch.no_grad():
            for class_idx in range(self.num_classes):
                # 生成噪声
                z = torch.randn(num_samples_per_class, self.latent_dim, device=self.device)

                # 创建类别标签
                labels = torch.full((num_samples_per_class,), class_idx, device=self.device, dtype=torch.long)

                # 生成样本
                generated_samples = self.generator(z, labels)

                # 转换为numpy
                generated_samples = generated_samples.squeeze(1).cpu().numpy()

                all_generated_data.append(generated_samples)
                all_generated_labels.extend([class_idx] * num_samples_per_class)

        generated_data = np.concatenate(all_generated_data, axis=0)
        generated_labels = np.array(all_generated_labels)

        return generated_data, generated_labels

    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'generator_state_dict': self.generator.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'optimizer_G_state_dict': self.optimizer_G.state_dict(),
            'optimizer_C_state_dict': self.optimizer_C.state_dict(),
            'config': self.config
        }, save_path)

    def load_model(self, load_path: str):
        """加载模型"""
        checkpoint = torch.load(load_path, map_location=self.device)
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.optimizer_G.load_state_dict(checkpoint['optimizer_G_state_dict'])
        self.optimizer_C.load_state_dict(checkpoint['optimizer_C_state_dict'])


class DCGAN(BaseAugmentationMethod):
    """Deep Convolutional GAN"""

    def __init__(self, config: Dict, device: torch.device):
        super().__init__(config, device)

        self.signal_length = config['dataset']['data_loading']['signal_length']
        self.num_classes = config['dataset']['datasets'][config['dataset']['name']]['num_classes']

        # 获取DCGAN配置
        dcgan_config = config['augmentation']['dcgan']
        self.latent_dim = dcgan_config['latent_dim']
        self.generator_lr = dcgan_config['generator_lr']
        self.discriminator_lr = dcgan_config['discriminator_lr']
        self.beta1 = dcgan_config['beta1']
        self.epochs = dcgan_config['epochs']

        # 创建模型（使用更深的网络）
        self.generator = self._create_dcgan_generator().to(device)
        self.discriminator = self._create_dcgan_discriminator().to(device)

        # 损失函数
        self.adversarial_loss = nn.BCELoss()

        # 优化器
        self.optimizer_G = optim.Adam(self.generator.parameters(), lr=self.generator_lr, betas=(self.beta1, 0.999))
        self.optimizer_D = optim.Adam(self.discriminator.parameters(), lr=self.discriminator_lr, betas=(self.beta1, 0.999))

        logger.info(f"DCGAN初始化完成，潜在维度: {self.latent_dim}, 类别数: {self.num_classes}")

    def _create_dcgan_generator(self):
        """创建DCGAN生成器"""
        class DCGANGenerator(nn.Module):
            def __init__(self, latent_dim, signal_length, num_classes):
                super().__init__()
                self.latent_dim = latent_dim
                self.signal_length = signal_length
                self.num_classes = num_classes

                # 标签嵌入
                if num_classes > 0:
                    self.label_emb = nn.Embedding(num_classes, latent_dim)

                # 计算初始大小
                self.init_size = signal_length // 32  # 5次上采样

                self.fc = nn.Sequential(
                    nn.Linear(latent_dim, 512 * self.init_size),
                    nn.BatchNorm1d(512 * self.init_size),
                    nn.ReLU(True)
                )

                self.conv_blocks = nn.Sequential(
                    # 第一层
                    nn.ConvTranspose1d(512, 256, 4, 2, 1, bias=False),
                    nn.BatchNorm1d(256),
                    nn.ReLU(True),

                    # 第二层
                    nn.ConvTranspose1d(256, 128, 4, 2, 1, bias=False),
                    nn.BatchNorm1d(128),
                    nn.ReLU(True),

                    # 第三层
                    nn.ConvTranspose1d(128, 64, 4, 2, 1, bias=False),
                    nn.BatchNorm1d(64),
                    nn.ReLU(True),

                    # 第四层
                    nn.ConvTranspose1d(64, 32, 4, 2, 1, bias=False),
                    nn.BatchNorm1d(32),
                    nn.ReLU(True),

                    # 输出层
                    nn.ConvTranspose1d(32, 1, 4, 2, 1, bias=False),
                    nn.Tanh()
                )

            def forward(self, noise, labels=None):
                if self.num_classes > 0 and labels is not None:
                    label_emb = self.label_emb(labels)
                    gen_input = torch.mul(label_emb, noise)
                else:
                    gen_input = noise

                out = self.fc(gen_input)
                out = out.view(out.shape[0], 512, self.init_size)
                signal = self.conv_blocks(out)

                # 调整到目标长度
                if signal.size(-1) != self.signal_length:
                    signal = nn.functional.interpolate(signal, size=self.signal_length, mode='linear', align_corners=False)

                return signal

        return DCGANGenerator(self.latent_dim, self.signal_length, self.num_classes)

    def _create_dcgan_discriminator(self):
        """创建DCGAN判别器"""
        class DCGANDiscriminator(nn.Module):
            def __init__(self, signal_length, num_classes):
                super().__init__()
                self.signal_length = signal_length
                self.num_classes = num_classes

                # 标签嵌入
                if num_classes > 0:
                    self.label_emb = nn.Embedding(num_classes, signal_length)

                self.conv_blocks = nn.Sequential(
                    # 第一层
                    nn.Conv1d(1 if num_classes == 0 else 2, 32, 4, 2, 1, bias=False),
                    nn.LeakyReLU(0.2, inplace=True),

                    # 第二层
                    nn.Conv1d(32, 64, 4, 2, 1, bias=False),
                    nn.BatchNorm1d(64),
                    nn.LeakyReLU(0.2, inplace=True),

                    # 第三层
                    nn.Conv1d(64, 128, 4, 2, 1, bias=False),
                    nn.BatchNorm1d(128),
                    nn.LeakyReLU(0.2, inplace=True),

                    # 第四层
                    nn.Conv1d(128, 256, 4, 2, 1, bias=False),
                    nn.BatchNorm1d(256),
                    nn.LeakyReLU(0.2, inplace=True),

                    # 第五层
                    nn.Conv1d(256, 512, 4, 2, 1, bias=False),
                    nn.BatchNorm1d(512),
                    nn.LeakyReLU(0.2, inplace=True),
                )

                self.adv_layer = nn.Sequential(
                    nn.AdaptiveAvgPool1d(1),
                    nn.Flatten(),
                    nn.Linear(512, 1),
                    nn.Sigmoid()
                )

            def forward(self, signal, labels=None):
                if self.num_classes > 0 and labels is not None:
                    label_emb = self.label_emb(labels).unsqueeze(1)
                    d_in = torch.cat((signal, label_emb), dim=1)
                else:
                    d_in = signal

                features = self.conv_blocks(d_in)
                validity = self.adv_layer(features)

                return validity

        return DCGANDiscriminator(self.signal_length, self.num_classes)

    def train(self, train_loader, val_loader=None) -> Dict:
        """训练DCGAN"""
        logger.info("开始训练DCGAN...")

        # 训练历史
        g_losses = []
        d_losses = []

        for epoch in range(self.epochs):
            epoch_g_loss = 0.0
            epoch_d_loss = 0.0
            num_batches = 0

            for i, (real_signals, labels) in enumerate(train_loader):
                batch_size = real_signals.size(0)
                real_signals = real_signals.to(self.device)
                labels = labels.to(self.device)

                # 确保信号有正确的维度
                if real_signals.dim() == 2:
                    real_signals = real_signals.unsqueeze(1)

                # 真实和虚假标签
                valid = torch.ones(batch_size, 1, device=self.device)
                fake = torch.zeros(batch_size, 1, device=self.device)

                # ---------------------
                #  训练判别器
                # ---------------------
                self.optimizer_D.zero_grad()

                # 真实信号损失
                real_validity = self.discriminator(real_signals, labels)
                d_real_loss = self.adversarial_loss(real_validity, valid)

                # 生成虚假信号
                z = torch.randn(batch_size, self.latent_dim, device=self.device)
                gen_labels = torch.randint(0, self.num_classes, (batch_size,), device=self.device)
                gen_signals = self.generator(z, gen_labels)

                # 虚假信号损失
                fake_validity = self.discriminator(gen_signals.detach(), gen_labels)
                d_fake_loss = self.adversarial_loss(fake_validity, fake)

                # 总判别器损失
                d_loss = (d_real_loss + d_fake_loss) / 2

                d_loss.backward()
                self.optimizer_D.step()

                # ---------------------
                #  训练生成器
                # ---------------------
                self.optimizer_G.zero_grad()

                # 生成器损失
                validity = self.discriminator(gen_signals, gen_labels)
                g_loss = self.adversarial_loss(validity, valid)

                g_loss.backward()
                self.optimizer_G.step()

                # 累计损失
                epoch_g_loss += g_loss.item()
                epoch_d_loss += d_loss.item()
                num_batches += 1

            # 记录平均损失
            avg_g_loss = epoch_g_loss / num_batches
            avg_d_loss = epoch_d_loss / num_batches
            g_losses.append(avg_g_loss)
            d_losses.append(avg_d_loss)

            logger.info(f"Epoch {epoch+1}/{self.epochs}: G_loss: {avg_g_loss:.4f}, D_loss: {avg_d_loss:.4f}")

        logger.info("DCGAN训练完成")

        return {
            'generator_losses': g_losses,
            'discriminator_losses': d_losses,
            'total_epochs': self.epochs
        }

    def generate_samples(self, num_samples_per_class: int, signal_length: int,
                        class_labels: Optional[torch.Tensor] = None) -> Tuple[np.ndarray, np.ndarray]:
        """生成样本"""
        self.generator.eval()

        all_generated_data = []
        all_generated_labels = []

        with torch.no_grad():
            for class_idx in range(self.num_classes):
                # 生成噪声
                z = torch.randn(num_samples_per_class, self.latent_dim, device=self.device)

                # 创建类别标签
                labels = torch.full((num_samples_per_class,), class_idx, device=self.device, dtype=torch.long)

                # 生成样本
                generated_samples = self.generator(z, labels)

                # 转换为numpy
                generated_samples = generated_samples.squeeze(1).cpu().numpy()

                all_generated_data.append(generated_samples)
                all_generated_labels.extend([class_idx] * num_samples_per_class)

        generated_data = np.concatenate(all_generated_data, axis=0)
        generated_labels = np.array(all_generated_labels)

        return generated_data, generated_labels

    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'generator_state_dict': self.generator.state_dict(),
            'discriminator_state_dict': self.discriminator.state_dict(),
            'optimizer_G_state_dict': self.optimizer_G.state_dict(),
            'optimizer_D_state_dict': self.optimizer_D.state_dict(),
            'config': self.config
        }, save_path)

    def load_model(self, load_path: str):
        """加载模型"""
        checkpoint = torch.load(load_path, map_location=self.device)
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
        self.optimizer_G.load_state_dict(checkpoint['optimizer_G_state_dict'])
        self.optimizer_D.load_state_dict(checkpoint['optimizer_D_state_dict'])


class DDPM(BaseAugmentationMethod):
    """原始DDPM（无条件扩散模型）"""

    def __init__(self, config: Dict, device: torch.device):
        super().__init__(config, device)

        self.signal_length = config['dataset']['data_loading']['signal_length']
        self.num_classes = config['dataset']['datasets'][config['dataset']['name']]['num_classes']

        # 获取DDPM配置
        ddpm_config = config['augmentation']['ddpm']
        self.timesteps = ddpm_config['timesteps']
        self.beta_start = ddpm_config['beta_start']
        self.beta_end = ddpm_config['beta_end']
        self.learning_rate = ddpm_config['learning_rate']
        self.epochs = ddpm_config['epochs']

        # 创建噪声调度
        self.betas = torch.linspace(self.beta_start, self.beta_end, self.timesteps, device=device)
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.alphas_cumprod_prev = torch.cat([torch.ones(1, device=device), self.alphas_cumprod[:-1]])

        # 创建UNet模型（简化版）
        self.model = self._create_unet().to(device)

        # 优化器
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)

        logger.info(f"DDPM初始化完成，时间步数: {self.timesteps}, 信号长度: {self.signal_length}")

    def _create_unet(self):
        """创建简化的UNet模型"""
        class SimpleUNet1D(nn.Module):
            def __init__(self, signal_length, timesteps):
                super().__init__()
                self.signal_length = signal_length
                self.timesteps = timesteps

                # 时间嵌入
                self.time_embed = nn.Sequential(
                    nn.Linear(timesteps, 128),
                    nn.ReLU(),
                    nn.Linear(128, 128)
                )

                # 编码器
                self.encoder = nn.Sequential(
                    nn.Conv1d(1, 64, 3, padding=1),
                    nn.ReLU(),
                    nn.Conv1d(64, 128, 3, padding=1),
                    nn.ReLU(),
                    nn.Conv1d(128, 256, 3, padding=1),
                    nn.ReLU(),
                )

                # 中间层
                self.middle = nn.Sequential(
                    nn.Conv1d(256 + 128, 256, 3, padding=1),
                    nn.ReLU(),
                    nn.Conv1d(256, 256, 3, padding=1),
                    nn.ReLU(),
                )

                # 解码器
                self.decoder = nn.Sequential(
                    nn.Conv1d(256, 128, 3, padding=1),
                    nn.ReLU(),
                    nn.Conv1d(128, 64, 3, padding=1),
                    nn.ReLU(),
                    nn.Conv1d(64, 1, 3, padding=1),
                )

            def forward(self, x, t):
                # 时间嵌入
                t_emb = self.time_embed(t)  # [batch_size, 128]
                t_emb = t_emb.unsqueeze(-1).expand(-1, -1, x.size(-1))  # [batch_size, 128, signal_length]

                # 编码
                h = self.encoder(x)  # [batch_size, 256, signal_length]

                # 添加时间信息
                h = torch.cat([h, t_emb], dim=1)  # [batch_size, 256+128, signal_length]

                # 中间层
                h = self.middle(h)

                # 解码
                out = self.decoder(h)

                return out

        return SimpleUNet1D(self.signal_length, self.timesteps)

    def q_sample(self, x_start, t, noise=None):
        """前向扩散过程"""
        if noise is None:
            noise = torch.randn_like(x_start)

        sqrt_alphas_cumprod_t = torch.sqrt(self.alphas_cumprod[t]).view(-1, 1, 1)
        sqrt_one_minus_alphas_cumprod_t = torch.sqrt(1.0 - self.alphas_cumprod[t]).view(-1, 1, 1)

        return sqrt_alphas_cumprod_t * x_start + sqrt_one_minus_alphas_cumprod_t * noise

    def p_losses(self, x_start, t, noise=None):
        """计算损失"""
        if noise is None:
            noise = torch.randn_like(x_start)

        x_noisy = self.q_sample(x_start=x_start, t=t, noise=noise)

        # 创建时间嵌入
        t_onehot = torch.zeros(x_start.size(0), self.timesteps, device=self.device)
        t_onehot.scatter_(1, t.unsqueeze(1), 1)

        predicted_noise = self.model(x_noisy, t_onehot)

        loss = nn.functional.mse_loss(noise, predicted_noise)

        return loss

    def train(self, train_loader, val_loader=None) -> Dict:
        """训练DDPM"""
        logger.info("开始训练DDPM...")

        # 训练历史
        losses = []

        for epoch in range(self.epochs):
            epoch_loss = 0.0
            num_batches = 0

            for i, (signals, _) in enumerate(train_loader):  # 忽略标签，因为是无条件生成
                signals = signals.to(self.device)

                # 确保信号有正确的维度
                if signals.dim() == 2:
                    signals = signals.unsqueeze(1)

                batch_size = signals.size(0)

                # 随机采样时间步
                t = torch.randint(0, self.timesteps, (batch_size,), device=self.device).long()

                self.optimizer.zero_grad()

                # 计算损失
                loss = self.p_losses(signals, t)

                loss.backward()
                self.optimizer.step()

                epoch_loss += loss.item()
                num_batches += 1

            # 记录平均损失
            avg_loss = epoch_loss / num_batches
            losses.append(avg_loss)

            logger.info(f"Epoch {epoch+1}/{self.epochs}: Loss: {avg_loss:.6f}")

        logger.info("DDPM训练完成")

        return {
            'losses': losses,
            'total_epochs': self.epochs
        }

    @torch.no_grad()
    def p_sample(self, x, t):
        """反向扩散采样"""
        betas_t = self.betas[t].view(-1, 1, 1)
        sqrt_one_minus_alphas_cumprod_t = torch.sqrt(1.0 - self.alphas_cumprod[t]).view(-1, 1, 1)
        sqrt_recip_alphas_t = torch.sqrt(1.0 / self.alphas[t]).view(-1, 1, 1)

        # 创建时间嵌入
        t_onehot = torch.zeros(x.size(0), self.timesteps, device=self.device)
        t_onehot.scatter_(1, t.unsqueeze(1), 1)

        # 预测噪声
        model_mean = sqrt_recip_alphas_t * (x - betas_t * self.model(x, t_onehot) / sqrt_one_minus_alphas_cumprod_t)

        if t[0] == 0:
            return model_mean
        else:
            posterior_variance_t = self.betas[t].view(-1, 1, 1)
            noise = torch.randn_like(x)
            return model_mean + torch.sqrt(posterior_variance_t) * noise

    @torch.no_grad()
    def p_sample_loop(self, shape):
        """完整的反向扩散过程"""
        device = next(self.model.parameters()).device

        img = torch.randn(shape, device=device)

        for i in reversed(range(0, self.timesteps)):
            t = torch.full((shape[0],), i, device=device, dtype=torch.long)
            img = self.p_sample(img, t)

        return img

    def generate_samples(self, num_samples_per_class: int, signal_length: int,
                        class_labels: Optional[torch.Tensor] = None) -> Tuple[np.ndarray, np.ndarray]:
        """生成样本"""
        self.model.eval()

        # 总样本数（无条件生成，忽略类别）
        total_samples = num_samples_per_class * self.num_classes

        # 生成样本
        shape = (total_samples, 1, signal_length)
        generated_samples = self.p_sample_loop(shape)

        # 转换为numpy
        generated_data = generated_samples.squeeze(1).cpu().numpy()

        # 随机分配标签（因为是无条件生成）
        generated_labels = np.random.randint(0, self.num_classes, total_samples)

        return generated_data, generated_labels

    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'betas': self.betas,
            'alphas_cumprod': self.alphas_cumprod,
            'config': self.config
        }, save_path)

    def load_model(self, load_path: str):
        """加载模型"""
        checkpoint = torch.load(load_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.betas = checkpoint['betas']
        self.alphas_cumprod = checkpoint['alphas_cumprod']


class CVAE(BaseAugmentationMethod):
    """条件变分自编码器"""

    def __init__(self, config: Dict, device: torch.device):
        super().__init__(config, device)

        self.signal_length = config['dataset']['data_loading']['signal_length']
        self.num_classes = config['dataset']['datasets'][config['dataset']['name']]['num_classes']

        # 获取CVAE配置
        cvae_config = config['augmentation']['cvae']
        self.latent_dim = cvae_config['latent_dim']
        self.hidden_dim = cvae_config['hidden_dim']
        self.learning_rate = cvae_config['learning_rate']
        self.epochs = cvae_config['epochs']
        self.beta = cvae_config.get('beta', 1.0)  # KL散度权重

        # 创建编码器和解码器
        self.encoder = self._create_encoder().to(device)
        self.decoder = self._create_decoder().to(device)

        # 优化器
        self.optimizer = optim.Adam(
            list(self.encoder.parameters()) + list(self.decoder.parameters()),
            lr=self.learning_rate
        )

        logger.info(f"CVAE初始化完成，潜在维度: {self.latent_dim}, 类别数: {self.num_classes}")

    def _create_encoder(self):
        """创建编码器"""
        class CVAEEncoder(nn.Module):
            def __init__(self, signal_length, num_classes, latent_dim, hidden_dim):
                super().__init__()
                self.signal_length = signal_length
                self.num_classes = num_classes

                # 标签嵌入
                self.label_emb = nn.Embedding(num_classes, signal_length)

                # 编码网络
                self.conv_layers = nn.Sequential(
                    nn.Conv1d(2, 64, 3, stride=2, padding=1),  # 信号+标签
                    nn.ReLU(),
                    nn.Conv1d(64, 128, 3, stride=2, padding=1),
                    nn.ReLU(),
                    nn.Conv1d(128, 256, 3, stride=2, padding=1),
                    nn.ReLU(),
                    nn.AdaptiveAvgPool1d(1),
                    nn.Flatten()
                )

                # 均值和方差
                self.fc_mu = nn.Linear(256, latent_dim)
                self.fc_logvar = nn.Linear(256, latent_dim)

            def forward(self, x, labels):
                # 标签嵌入
                label_emb = self.label_emb(labels).unsqueeze(1)

                # 拼接信号和标签
                x_labeled = torch.cat([x, label_emb], dim=1)

                # 编码
                h = self.conv_layers(x_labeled)

                mu = self.fc_mu(h)
                logvar = self.fc_logvar(h)

                return mu, logvar

        return CVAEEncoder(self.signal_length, self.num_classes, self.latent_dim, self.hidden_dim)

    def _create_decoder(self):
        """创建解码器"""
        class CVAEDecoder(nn.Module):
            def __init__(self, signal_length, num_classes, latent_dim, hidden_dim):
                super().__init__()
                self.signal_length = signal_length
                self.num_classes = num_classes

                # 标签嵌入
                self.label_emb = nn.Embedding(num_classes, latent_dim)

                # 解码网络
                self.fc = nn.Linear(latent_dim * 2, hidden_dim)

                # 计算初始大小
                self.init_size = signal_length // 8
                self.fc_reshape = nn.Linear(hidden_dim, 256 * self.init_size)

                self.conv_layers = nn.Sequential(
                    nn.ConvTranspose1d(256, 128, 3, stride=2, padding=1, output_padding=1),
                    nn.ReLU(),
                    nn.ConvTranspose1d(128, 64, 3, stride=2, padding=1, output_padding=1),
                    nn.ReLU(),
                    nn.ConvTranspose1d(64, 1, 3, stride=2, padding=1, output_padding=1),
                    nn.Tanh()
                )

            def forward(self, z, labels):
                # 标签嵌入
                label_emb = self.label_emb(labels)

                # 拼接潜在向量和标签
                z_labeled = torch.cat([z, label_emb], dim=1)

                # 解码
                h = torch.relu(self.fc(z_labeled))
                h = self.fc_reshape(h)
                h = h.view(h.size(0), 256, self.init_size)

                x_recon = self.conv_layers(h)

                # 调整到目标长度
                if x_recon.size(-1) != self.signal_length:
                    x_recon = nn.functional.interpolate(x_recon, size=self.signal_length, mode='linear', align_corners=False)

                return x_recon

        return CVAEDecoder(self.signal_length, self.num_classes, self.latent_dim, self.hidden_dim)

    def reparameterize(self, mu, logvar):
        """重参数化技巧"""
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def forward(self, x, labels):
        """前向传播"""
        mu, logvar = self.encoder(x, labels)
        z = self.reparameterize(mu, logvar)
        x_recon = self.decoder(z, labels)
        return x_recon, mu, logvar

    def loss_function(self, x_recon, x, mu, logvar):
        """CVAE损失函数"""
        # 重构损失
        recon_loss = nn.functional.mse_loss(x_recon, x, reduction='sum')

        # KL散度
        kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())

        return recon_loss + self.beta * kl_loss, recon_loss, kl_loss

    def train(self, train_loader, val_loader=None) -> Dict:
        """训练CVAE"""
        logger.info("开始训练CVAE...")

        # 训练历史
        total_losses = []
        recon_losses = []
        kl_losses = []

        for epoch in range(self.epochs):
            epoch_total_loss = 0.0
            epoch_recon_loss = 0.0
            epoch_kl_loss = 0.0
            num_batches = 0

            for i, (signals, labels) in enumerate(train_loader):
                signals = signals.to(self.device)
                labels = labels.to(self.device)

                # 确保信号有正确的维度
                if signals.dim() == 2:
                    signals = signals.unsqueeze(1)

                self.optimizer.zero_grad()

                # 前向传播
                x_recon, mu, logvar = self.forward(signals, labels)

                # 计算损失
                total_loss, recon_loss, kl_loss = self.loss_function(x_recon, signals, mu, logvar)

                # 反向传播
                total_loss.backward()
                self.optimizer.step()

                # 累计损失
                epoch_total_loss += total_loss.item()
                epoch_recon_loss += recon_loss.item()
                epoch_kl_loss += kl_loss.item()
                num_batches += 1

            # 记录平均损失
            avg_total_loss = epoch_total_loss / num_batches
            avg_recon_loss = epoch_recon_loss / num_batches
            avg_kl_loss = epoch_kl_loss / num_batches

            total_losses.append(avg_total_loss)
            recon_losses.append(avg_recon_loss)
            kl_losses.append(avg_kl_loss)

            logger.info(f"Epoch {epoch+1}/{self.epochs}: "
                       f"Total: {avg_total_loss:.4f}, Recon: {avg_recon_loss:.4f}, KL: {avg_kl_loss:.4f}")

        logger.info("CVAE训练完成")

        return {
            'total_losses': total_losses,
            'recon_losses': recon_losses,
            'kl_losses': kl_losses,
            'total_epochs': self.epochs
        }

    def generate_samples(self, num_samples_per_class: int, signal_length: int,
                        class_labels: Optional[torch.Tensor] = None) -> Tuple[np.ndarray, np.ndarray]:
        """生成样本"""
        self.encoder.eval()
        self.decoder.eval()

        all_generated_data = []
        all_generated_labels = []

        with torch.no_grad():
            for class_idx in range(self.num_classes):
                # 从先验分布采样
                z = torch.randn(num_samples_per_class, self.latent_dim, device=self.device)

                # 创建类别标签
                labels = torch.full((num_samples_per_class,), class_idx, device=self.device, dtype=torch.long)

                # 生成样本
                generated_samples = self.decoder(z, labels)

                # 转换为numpy
                generated_samples = generated_samples.squeeze(1).cpu().numpy()

                all_generated_data.append(generated_samples)
                all_generated_labels.extend([class_idx] * num_samples_per_class)

        generated_data = np.concatenate(all_generated_data, axis=0)
        generated_labels = np.array(all_generated_labels)

        return generated_data, generated_labels

    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'encoder_state_dict': self.encoder.state_dict(),
            'decoder_state_dict': self.decoder.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config
        }, save_path)

    def load_model(self, load_path: str):
        """加载模型"""
        checkpoint = torch.load(load_path, map_location=self.device)
        self.encoder.load_state_dict(checkpoint['encoder_state_dict'])
        self.decoder.load_state_dict(checkpoint['decoder_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
