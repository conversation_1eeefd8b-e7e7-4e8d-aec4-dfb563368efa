# 组合数据集工作流程

## 概述

新的数据流程解决了原有分类器训练时数据来源不一致的问题，通过创建统一的组合数据集，确保基线分类器和增强分类器使用相同的数据源进行公平比较。

## 🔄 新的数据流程

### 1. 数据加载阶段
```
原始数据集 → 样本限制 → 完整训练数据 (full_train_data)
                     ↓
                  扩散模型训练用数据 (train_subset + val_subset)
```

### 2. 模型训练阶段
```
train_subset → 扩散模型训练 → 生成增强数据
```

### 3. 组合数据集创建阶段
```
完整训练数据 (full_train_data) + 生成数据 → 健康样本平衡调整 → 组合数据集
                                                              ↓
                                                        保存到文件系统
```

### 4. 分类器训练阶段
```
组合数据集 → 重新分割 → 训练集 + 验证集
                    ↓
                基线分类器 & 增强分类器训练
```

## 📁 目录结构

### 组合数据集保存路径
```
results/
└── combined_datasets/
    └── {dataset_name}_{method_name}/
        ├── combined_data.npy      # 组合数据
        ├── combined_labels.npy    # 组合标签
        └── dataset_info.json      # 数据集信息
```

### 示例
```
results/
└── combined_datasets/
    └── KAT_CDDPM/
        ├── combined_data.npy
        ├── combined_labels.npy
        └── dataset_info.json
```

## 🔧 关键函数

### 1. `save_combined_dataset()`
- **功能**: 保存组合数据集到文件系统
- **输入**: 组合数据、组合标签、配置
- **输出**: 数据集信息字典
- **位置**: `main.py`

### 2. `CombinedDataLoader`
- **功能**: 加载和管理组合数据集
- **方法**: 
  - `load_combined_dataset()`: 加载保存的组合数据
  - `create_data_loaders()`: 创建训练/验证/测试数据加载器
- **位置**: `common/data_loader.py`

### 3. `adjust_healthy_samples_for_balance()`
- **功能**: 动态调整健康样本数量以保持平衡
- **输入**: 增强数据、原始数据、配置
- **输出**: 平衡后的数据和标签
- **位置**: `main.py`

## ✅ 解决的问题

### 1. 数据来源一致性
- **原问题**: 基线分类器使用 `train_subset`，健康样本补充却从完整 `train_loader` 获取
- **解决方案**: 统一使用完整训练数据 `full_train_data` 作为基础

### 2. 重复分割问题
- **原问题**: 增强数据被二次分割，导致训练数据进一步减少
- **解决方案**: 组合数据集统一分割，避免重复操作

### 3. 数据泄露风险
- **原问题**: 健康样本补充可能引入验证集数据
- **解决方案**: 明确数据边界，确保验证集独立性

## 🎯 优势

### 1. 数据一致性
- 基线和增强分类器使用相同的数据源
- 确保公平比较

### 2. 可追溯性
- 组合数据集保存到文件，便于后续分析
- 包含完整的数据集信息

### 3. 灵活性
- 支持不同的健康样本处理策略
- 支持多种数据增强方法

### 4. 可重现性
- 固定随机种子确保结果可重现
- 数据流程清晰明确

## 📊 数据流程对比

### 原流程（有问题）
```
原始数据 → train_subset (70%) + val_subset (30%)
              ↓
         扩散模型训练 → 生成数据
              ↓
    train_subset + 生成数据 → 再次分割 (70%/30%)
              ↓
         增强分类器训练

健康样本补充 ← 完整训练数据 (100%) ❌ 数据来源不一致
```

### 新流程（已修复）
```
原始数据 → 完整训练数据 (100%)
              ↓
         train_subset (70%) → 扩散模型训练 → 生成数据
              ↓
    完整训练数据 + 生成数据 → 健康样本平衡 → 组合数据集
              ↓
         统一分割 (70%/30%)
              ↓
    基线分类器 & 增强分类器训练 ✅ 数据来源一致
```

## 🧪 测试验证

使用 `test_combined_dataset.py` 脚本验证新流程：

```bash
python test_combined_dataset.py
```

测试内容：
1. 原始数据加载
2. 模拟数据生成
3. 数据合并和平衡调整
4. 组合数据集保存和加载
5. 数据加载器创建
6. 数据一致性验证

## 📝 配置更新

在 `config.yaml` 中添加了组合数据集保存路径：

```yaml
system:
  save:
    combined_datasets_dir: "results/combined_datasets"  # 组合数据集保存目录
```

## 🚀 使用方法

1. **正常运行实验**：
   ```bash
   python main.py
   ```
   系统会自动创建和使用组合数据集

2. **测试数据流程**：
   ```bash
   python test_combined_dataset.py
   ```

3. **查看组合数据集**：
   ```bash
   ls results/combined_datasets/
   ```

## 📈 性能影响

- **存储**: 增加组合数据集的存储需求
- **内存**: 临时需要更多内存来处理完整数据集
- **时间**: 数据保存和加载增加少量时间开销
- **准确性**: 提高实验的科学性和可重现性

## 🔮 未来扩展

1. **数据集版本管理**: 支持多版本组合数据集
2. **增量更新**: 支持增量添加生成数据
3. **数据质量评估**: 自动评估组合数据集质量
4. **并行处理**: 支持大规模数据集的并行处理
