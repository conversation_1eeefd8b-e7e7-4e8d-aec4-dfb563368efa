    % ==================== 参数设置 ====================
    sample_length = 1024;           % 每个样本的长度（采样点数）
    train_samples_per_class = 200;  % 每个类别生成的训练样本数量
    test_samples_per_class = 200;    % 每个类别生成的测试样本数量
    train_ratio = 0.7;             % 训练集占总数据的比例
    
    % ==================== 路径设置 ====================
    % 获取脚本所在目录的上级目录作为基础路径
    script_dir = fileparts(mfilename('fullpath'));
    base_dir = fileparts(script_dir);  % 上级目录
    
    % 使用基础路径构建完整路径
    base_path = fullfile(base_dir, 'dataset', 'MFPT', 'ori_mat', 'all');    % 原始数据所在的根目录
    train_path = fullfile(base_dir, 'dataset', 'MFPT', 'ori_mat', 'train'); % 训练样本保存目录
    test_path = fullfile(base_dir, 'dataset', 'MFPT', 'ori_mat', 'test');   % 测试样本保存目录
    final_mat_path = fullfile(base_dir, 'dataset', 'MFPT', 'used_mat');     % 最终mat文件保存目录
    final_npy_path = fullfile(base_dir, 'dataset', 'MFPT', 'used_npy');     % 最终npy文件保存目录
    
    fprintf('使用以下路径:\n');
    fprintf('原始数据目录: %s\n', base_path);
    fprintf('Mat文件保存目录: %s\n', final_mat_path);
    fprintf('NPY文件保存目录: %s\n', final_npy_path);
    
    % 检查原始数据目录是否存在
    if ~exist(base_path, 'dir')
        error('原始数据目录不存在: %s', base_path);
    end
    
    % ==================== 创建可视化窗口 ====================
    figure('Name', '各类别样本示例', 'Position', [100, 100, 1200, 800]);  % 创建一个新的图形窗口，设置标题和大小
    
    % ==================== 获取数据目录结构 ====================
    class_folders = dir(base_path);  % 获取基础路径下的所有文件和文件夹
    class_folders = class_folders([class_folders.isdir]);  % 只保留文件夹
    class_folders = class_folders(~ismember({class_folders.name}, {'.', '..'}));  % 移除当前目录(.)和父目录(..)
    
    % ==================== 创建保存目录 ====================
    create_directories(train_path, test_path, final_mat_path);  % 创建必要的数据保存目录
    
    % 创建NPY文件保存目录
    if ~exist(fullfile(final_npy_path, 'train'), 'dir')
        mkdir(fullfile(final_npy_path, 'train'));
    end
    if ~exist(fullfile(final_npy_path, 'test'), 'dir')
        mkdir(fullfile(final_npy_path, 'test'));
    end
    
    % ==================== 初始化数据存储变量 ====================
    all_train_data = [];    % 存储所有类别的训练数据
    all_test_data = [];     % 存储所有类别的测试数据
    all_train_labels = [];  % 存储所有训练数据的标签
    all_test_labels = [];   % 存储所有测试数据的标签
    
    % ==================== 处理每个类别的数据 ====================
    for i = 1:length(class_folders)  % 遍历每个类别文件夹
        class_name = class_folders(i).name;  % 获取类别名称（作为标签）
        class_path = fullfile(base_path, class_name);  % 构建完整的类别路径
        
        % 创建当前类别的训练和测试数据保存目录
        mkdir(fullfile(train_path, class_name));  % 创建训练数据目录
        mkdir(fullfile(test_path, class_name));   % 创建测试数据目录
        
        % 获取当前类别下的所有.mat文件
        mat_files = dir(fullfile(class_path, '*.mat'));  % 获取所有.mat文件列表
        
        for j = 1:length(mat_files)  % 遍历当前类别下的所有.mat文件
            % 加载原始数据
            data_path = fullfile(class_path, mat_files(j).name);  % 构建完整的数据文件路径
            raw_data = load(data_path);  % 加载.mat文件
            time_series = raw_data.bearing.gs;  % 提取轴承振动信号数据
            
            % 划分训练集和测试集
            split_point = floor(length(time_series) * train_ratio);  % 计算划分点
            train_data = time_series(1:split_point);  % 提取训练数据
            test_data = time_series(split_point+1:end);  % 提取测试数据
            
            % 生成训练和测试样本
            train_samples = generate_samples(train_data, sample_length, train_samples_per_class);  % 生成训练样本
            test_samples = generate_samples(test_data, sample_length, test_samples_per_class);     % 生成测试样本
            
            % ==================== 可视化样本 ====================
            % 绘制训练样本示例
            subplot(length(class_folders), 2, 2*i-1);  % 创建训练样本子图
            plot(train_samples(1,:), 'b-');  % 绘制第一个训练样本波形
            title(['类别 ', class_name, ' - 训练样本示例']);  % 设置子图标题
            xlabel('采样点');  % 设置x轴标签
            ylabel('幅值');    % 设置y轴标签
            grid on;          % 显示网格
            
            % 绘制测试样本示例
            subplot(length(class_folders), 2, 2*i);  % 创建测试样本子图
            plot(test_samples(1,:), 'r-');  % 绘制第一个测试样本波形
            title(['类别 ', class_name, ' - 测试样本示例']);  % 设置子图标题
            xlabel('采样点');  % 设置x轴标签
            ylabel('幅值');    % 设置y轴标签
            grid on;          % 显示网格
            
            % 保存当前类别的样本
            save(fullfile(train_path, class_name, 'samples.mat'), 'train_samples');  % 保存训练样本
            save(fullfile(test_path, class_name, 'samples.mat'), 'test_samples');    % 保存测试样本
            
            % 将样本添加到总数据集
            all_train_data = [all_train_data; train_samples];  % 添加训练样本
            all_test_data = [all_test_data; test_samples];     % 添加测试样本
            
            % 生成并添加标签
            train_labels = int32(ones(size(train_samples, 1), 1) * (str2double(class_name)));  % 生成训练样本标签
            test_labels = int32(ones(size(test_samples, 1), 1) * (str2double(class_name)));    % 生成测试样本标签
            
            all_train_labels = [all_train_labels; train_labels];  % 添加训练标签
            all_test_labels = [all_test_labels; test_labels];     % 添加测试标签
        end
    end
    
    % ==================== 调整图形显示 ====================
    set(gcf, 'Units', 'Normalized');  % 设置图形单位为标准化
    set(gcf, 'Position', [0.1, 0.1, 0.8, 0.8]);  % 调整图形窗口大小和位置
    
    % 保存可视化结果
    saveas(gcf, fullfile(final_mat_path, 'samples_visualization.png'));  % 将图形保存为PNG文件
    
    % ==================== 确保标签类型 ====================
    all_train_labels = int32(all_train_labels);  % 确保训练标签为int32类型
    all_test_labels = int32(all_test_labels);    % 确保测试标签为int32类型
    
    % ==================== 保存最终数据集 ====================
    % 保存MAT格式
    save(fullfile(final_mat_path, 'train', 'train_dataset.mat'), 'all_train_data', 'all_train_labels');
    save(fullfile(final_mat_path, 'test', 'test_dataset.mat'), 'all_test_data', 'all_test_labels');
    
    % ==================== 生成并执行Python脚本来转换为NPY格式 ====================
    fprintf('生成并执行Python脚本来转换为NPY格式...\n');
    
    % 创建临时Python脚本文件
    temp_dir = fullfile(tempdir, 'mfpt_temp');
    if ~exist(temp_dir, 'dir')
        mkdir(temp_dir);
    end
    
    py_script_path = fullfile(temp_dir, 'convert_to_numpy.py');
    fid = fopen(py_script_path, 'w');
    
    if fid < 0
        error('无法创建Python转换脚本: %s', py_script_path);
    end
    
    % 写入Python脚本内容
    fprintf(fid, '#!/usr/bin/env python\n');
    fprintf(fid, '# -*- coding: utf-8 -*-\n\n');
    fprintf(fid, '# 此脚本由MATLAB自动生成，用于将MFPT数据集从MAT格式转换为NPY格式\n\n');
    
    fprintf(fid, 'import numpy as np\n');
    fprintf(fid, 'import scipy.io as sio\n');
    fprintf(fid, 'import os\n\n');
    
    fprintf(fid, '# 创建输出目录\n');
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n', strrep(fullfile(final_npy_path, 'train'), '\', '\\'));
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n\n', strrep(fullfile(final_npy_path, 'test'), '\', '\\'));
    
    fprintf(fid, '# 加载训练数据\n');
    fprintf(fid, 'print("加载训练数据...")\n');
    fprintf(fid, 'train_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'train', 'train_dataset.mat'), '\', '\\'));
    
    fprintf(fid, '# 加载测试数据\n');
    fprintf(fid, 'print("加载测试数据...")\n');
    fprintf(fid, 'test_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'test', 'test_dataset.mat'), '\', '\\'));
    
    fprintf(fid, '# 保存训练数据\n');
    fprintf(fid, 'print("保存训练数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", train_data["all_train_data"])\n', strrep(fullfile(final_npy_path, 'train', 'mfpt_data.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", train_data["all_train_labels"])\n\n', strrep(fullfile(final_npy_path, 'train', 'mfpt_label.npy'), '\', '\\'));
    
    fprintf(fid, '# 保存测试数据\n');
    fprintf(fid, 'print("保存测试数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", test_data["all_test_data"])\n', strrep(fullfile(final_npy_path, 'test', 'mfpt_data.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", test_data["all_test_labels"])\n\n', strrep(fullfile(final_npy_path, 'test', 'mfpt_label.npy'), '\', '\\'));
    
    fprintf(fid, 'print("转换完成！")\n');
    fclose(fid);
    
    % 创建说明文件
    readme_path = fullfile(final_npy_path, 'README.txt');
    fid = fopen(readme_path, 'w');
    if fid > 0
        fprintf(fid, 'MFPT数据集 - NPY格式数据说明\n');
        fprintf(fid, '==============================\n\n');
        fprintf(fid, '数据格式说明:\n');
        fprintf(fid, '- NPY文件格式: \n');
        fprintf(fid, '  * 训练数据: mfpt_data.npy (样本矩阵), mfpt_label.npy (整数类型标签)\n');
        fprintf(fid, '  * 测试数据: mfpt_data.npy (样本矩阵), mfpt_label.npy (整数类型标签)\n\n');
        fprintf(fid, '标签说明:\n');
        fprintf(fid, '- 0: 基线正常\n');
        fprintf(fid, '- 1: 内圈故障\n');
        fprintf(fid, '- 2: 外圈故障\n\n');
        fprintf(fid, '样本数量:\n');
        fprintf(fid, '- 训练集: 每个类别 %d 个样本 (总计 %d 个样本)\n', train_samples_per_class, train_samples_per_class*3);
        fprintf(fid, '- 测试集: 每个类别 %d 个样本 (总计 %d 个样本)\n\n', test_samples_per_class, test_samples_per_class*3);
        fprintf(fid, '采样方法:\n');
        fprintf(fid, '- 从每个类别的原始信号中随机提取样本，允许样本重叠\n');
        fprintf(fid, '- 每个样本包含 %d 个数据点\n', sample_length);
        fclose(fid);
    end
    
    % 执行Python脚本
    fprintf('正在执行Python脚本以生成NPY格式数据文件...\n');
    
    try
        % 判断系统平台
        if ispc
            % Windows系统
            [status, cmdout] = system(['python "', py_script_path, '"']);
        else
            % Linux/Mac系统
            [status, cmdout] = system(['python3 "', py_script_path, '"']);
        end
        
        if status == 0
            fprintf('Python脚本执行成功！\n%s\n', cmdout);
        else
            fprintf('Python脚本执行失败！错误码: %d\n%s\n', status, cmdout);
            fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
            fprintf('python "%s"\n', py_script_path);
        end
    catch e
        fprintf('执行Python脚本时出错: %s\n', e.message);
        fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
        fprintf('python "%s"\n', py_script_path);
    end
    
    % 尝试删除临时Python脚本
    try
        delete(py_script_path);
        rmdir(temp_dir);
        fprintf('临时Python脚本已删除\n');
    catch
        fprintf('无法删除临时Python脚本: %s\n', py_script_path);
    end
    
    % ==================== 输出处理结果 ====================
    fprintf('数据处理完成！\n');  % 输出完成信息
    fprintf('训练集大小: %d x %d\n', size(all_train_data));   % 输出训练集大小
    fprintf('测试集大小: %d x %d\n', size(all_test_data));    % 输出测试集大小


function samples = generate_samples(data, sample_length, num_samples)
    % 生成指定数量的随机样本
    samples = zeros(num_samples, sample_length);  % 预分配样本存储空间
    data_length = length(data);  % 获取原始数据长度
    
    for i = 1:num_samples  % 生成指定数量的样本
        start_idx = randi(data_length - sample_length + 1);  % 随机选择起始点
        samples(i, :) = data(start_idx:start_idx + sample_length - 1);  % 提取样本数据
    end
end

function create_directories(train_path, test_path, final_path)
    % 创建数据保存所需的所有目录
    if ~exist(train_path, 'dir')  % 如果训练数据目录不存在
        mkdir(train_path);        % 创建训练数据目录
    end
    
    if ~exist(test_path, 'dir')   % 如果测试数据目录不存在
        mkdir(test_path);         % 创建测试数据目录
    end
    
    if ~exist(fullfile(final_path, 'train'), 'dir')  % 如果最终训练数据目录不存在
        mkdir(fullfile(final_path, 'train'));        % 创建最终训练数据目录
    end
    
    if ~exist(fullfile(final_path, 'test'), 'dir')   % 如果最终测试数据目录不存在
        mkdir(fullfile(final_path, 'test'));         % 创建最终测试数据目录
    end
end 