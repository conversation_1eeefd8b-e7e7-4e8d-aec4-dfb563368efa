% JST数据集预处理程序 - 顺序采样版本
% 功能说明：
% 1. 处理JST轴承故障数据集，采用顺序采样方式而非随机采样
% 2. 支持设置重叠长度，按顺序依次提取样本
% 3. 训练集和测试集从不同的信号段提取，避免数据泄露
% 4. 三种状态：正常(0)、弱水力振动(1)、强水力振动(2)
% 5. 可配置样本长度、重叠长度、生成样本数量等参数
% 6. 数据保存为MATLAB和NPY格式，便于后续使用
% 
% 原样本包括三种状态：正常(0)、弱水力振动(1)、强水力振动(2)

% 清空工作区和命令窗口
clear;
clc;

try
    % 开始计时
    tic;
    
    % 调用处理函数
    process_JST_dataset_sequential();
    
    % 结束计时
    elapsed_time = toc;
    fprintf('处理完成! 耗时: %.2f 秒\n', elapsed_time);
catch e
    % 捕获并显示错误
    fprintf('处理过程中发生错误:\n');
    disp(e.message);
    if ~isempty(e.stack)
        fprintf('错误位置: %s (行 %d)\n', e.stack(1).name, e.stack(1).line);
    end
end

% ================== 主处理函数 ==================
function process_JST_dataset_sequential()
    % ==================== 参数设置 ====================
    sample_length = 1024;           % 每个样本的长度（采样点数）
    overlap_length = 256;           % 重叠长度（采样点数），控制相邻样本的重叠程度
    train_ratio = 0.5;              % 训练集从前70%长度选择，测试集从后30%长度选择
    random_seed = 42;               % 随机种子，确保结果可重现（用于数据打乱）
    Location_s = 5;                 % 数据位置固定为5
    
    % 样本数量控制参数（设置为0表示不限制，提取所有可能的样本）
    max_samples_per_signal = 0;     % 每个信号最多提取的样本数（0表示提取所有可能的样本）
    train_samples_per_class = 0;    % 每个类别最多生成的训练样本数（0表示不限制）
    test_samples_per_class = 0;     % 每个类别最多生成的测试样本数（0表示不限制）
    
    % ==================== 路径设置 ====================
    % 获取脚本所在目录的上级目录作为基础路径
    script_dir = fileparts(mfilename('fullpath'));
    base_dir = fileparts(script_dir);  % 上级目录
    
    % 使用基础路径构建完整路径
    ori_path = fullfile(base_dir, 'dataset', 'JST', 'ori_mat');      % 原始数据目录
    final_mat_path = fullfile(base_dir, 'dataset', 'JST', 'used_mat_sequential');  % 最终mat文件保存目录
    final_npy_path = fullfile(base_dir, 'dataset', 'JST', 'used_npy_sequential');  % 最终npy文件保存目录
    
    fprintf('使用以下路径:\n');
    fprintf('原始数据目录: %s\n', ori_path);
    fprintf('Mat文件保存目录: %s\n', final_mat_path);
    fprintf('NPY文件保存目录: %s\n', final_npy_path);
    fprintf('参数设置:\n');
    fprintf('- 样本长度: %d 个采样点\n', sample_length);
    fprintf('- 重叠长度: %d 个采样点\n', overlap_length);
    fprintf('- 步长: %d 个采样点\n', sample_length - overlap_length);
    fprintf('- 训练集从前%.0f%%选取，测试集从后%.0f%%选取\n', train_ratio*100, (1-train_ratio)*100);
    fprintf('- 数据位置: %d\n', Location_s);
    if max_samples_per_signal > 0
        fprintf('- 每个信号最多提取: %d 个样本\n', max_samples_per_signal);
    else
        fprintf('- 每个信号提取: 所有可能的样本\n');
    end
    if train_samples_per_class > 0
        fprintf('- 每个类别最多训练样本: %d 个\n', train_samples_per_class);
    else
        fprintf('- 每个类别训练样本: 不限制数量\n');
    end
    if test_samples_per_class > 0
        fprintf('- 每个类别最多测试样本: %d 个\n', test_samples_per_class);
    else
        fprintf('- 每个类别测试样本: 不限制数量\n');
    end
    
    % 检查原始数据目录是否存在
    if ~exist(ori_path, 'dir')
        error('原始数据目录不存在: %s', ori_path);
    end
    
    % 检查参数合理性
    if overlap_length >= sample_length
        error('重叠长度(%d)不能大于等于样本长度(%d)', overlap_length, sample_length);
    end
    
    fprintf('开始处理JST数据集...\n');
    
    % ==================== 创建保存目录 ====================
    if ~exist(fullfile(final_mat_path, 'train'), 'dir')
        mkdir(fullfile(final_mat_path, 'train'));
    end
    if ~exist(fullfile(final_mat_path, 'test'), 'dir')
        mkdir(fullfile(final_mat_path, 'test'));
    end
    if ~exist(fullfile(final_npy_path, 'train'), 'dir')
        mkdir(fullfile(final_npy_path, 'train'));
    end
    if ~exist(fullfile(final_npy_path, 'test'), 'dir')
        mkdir(fullfile(final_npy_path, 'test'));
    end
    
    % ==================== 读取原始数据 ====================
    fprintf('读取原始数据文件...\n');
    
    % 用于存储所有类别的原始信号和标签
    class_signals = cell(3, 1);  % 存储3个类别的原始信号
    
    % 处理三种不同类别的数据
    for class_id = 0:2
        fprintf('处理类别 %d 的数据...\n', class_id);
        
        % 构建类别数据路径
        class_path = fullfile(ori_path, num2str(class_id));
        
        % 检查类别目录是否存在
        if ~exist(class_path, 'dir')
            warning('类别 %d 的目录不存在: %s', class_id, class_path);
            continue;
        end
        
        % 查找JST_STRdata_matrix.mat文件
        mat_file_path = fullfile(class_path, 'JST_STRdata_matrix.mat');
        if ~exist(mat_file_path, 'file')
            warning('类别 %d 的数据文件不存在: %s', class_id, mat_file_path);
            continue;
        end
        
        % 加载数据文件
        fprintf('加载文件: %s\n', mat_file_path);
        try
            data = load(mat_file_path);
        catch e
            warning('无法加载类别 %d 的数据文件: %s\n错误: %s', class_id, mat_file_path, e.message);
            continue;
        end
        
        % 根据不同类别提取数据
        try
            if class_id == 0
                a1 = data.STRdata_Sall_matrix_fast(13.7e4:17e4, Location_s)';
            else % 类别1和2
                a1 = data.STRdata_Sall_matrix_fast(:, Location_s)';
            end
            
            % 存储类别信号
            class_signals{class_id+1} = a1;
            
            fprintf('类别 %d: 提取了 %d 个数据点\n', class_id, length(a1));
        catch e
            warning('处理类别 %d 的数据时出错: %s', class_id, e.message);
            continue;
        end
    end
    
    % 检查是否成功提取了样本
    if all(cellfun(@isempty, class_signals))
        error('未能成功提取任何样本。请检查数据文件和处理逻辑。');
    end
    
    % ==================== 从信号中顺序选择样本 ====================
    fprintf('从信号中顺序选择样本...\n');
    
    % 设置随机种子以确保结果可重现（用于最终打乱）
    rng(random_seed);
    
    % 准备存储训练和测试样本
    all_train_data = [];
    all_train_labels = [];
    all_test_data = [];
    all_test_labels = [];
    
    % 按类别处理
    for class_id = 0:2
        fprintf('处理类别 %d 的样本...\n', class_id);
        
        % 获取当前类别的信号
        signal = class_signals{class_id+1};
        
        if isempty(signal)
            warning('类别 %d 的信号为空，跳过处理', class_id);
            continue;
        end
        
        % 确保信号长度足够
        if length(signal) < sample_length
            warning('类别 %d 的信号长度不足 (%d < %d)，跳过处理', ...
                class_id, length(signal), sample_length);
            continue;
        end
        
        % 计算训练和测试区域的分割点
        split_point = floor(length(signal) * train_ratio);
        
        % 从信号的前train_ratio部分提取训练样本（顺序采样）
        train_signal = signal(1:split_point);
        train_samples = extract_sequential_samples_jst(train_signal, sample_length, overlap_length, max_samples_per_signal);
        
        % 检查训练样本数量限制
        if train_samples_per_class > 0 && size(train_samples, 1) > train_samples_per_class
            train_samples = train_samples(1:train_samples_per_class, :);
            fprintf('    训练样本已达到限制数量 %d\n', train_samples_per_class);
        end
        train_labels = ones(size(train_samples, 1), 1) * class_id;
        
        % 从信号的后(1-train_ratio)部分提取测试样本（顺序采样）
        test_signal = signal(split_point+1:end);
        test_samples = extract_sequential_samples_jst(test_signal, sample_length, overlap_length, max_samples_per_signal);
        
        % 检查测试样本数量限制
        if test_samples_per_class > 0 && size(test_samples, 1) > test_samples_per_class
            test_samples = test_samples(1:test_samples_per_class, :);
            fprintf('    测试样本已达到限制数量 %d\n', test_samples_per_class);
        end
        test_labels = ones(size(test_samples, 1), 1) * class_id;
        
        % 添加到总样本集
        all_train_data = [all_train_data; train_samples];
        all_train_labels = [all_train_labels; train_labels];
        all_test_data = [all_test_data; test_samples];
        all_test_labels = [all_test_labels; test_labels];
        
        fprintf('类别 %d: 生成了 %d 个训练样本和 %d 个测试样本\n', ...
            class_id, size(train_samples, 1), size(test_samples, 1));
    end
    
    % 随机打乱训练集和测试集
    train_shuffle_idx = randperm(size(all_train_data, 1));
    all_train_data = all_train_data(train_shuffle_idx, :);
    all_train_labels = int32(all_train_labels(train_shuffle_idx));  % 转换为int32类型
    
    test_shuffle_idx = randperm(size(all_test_data, 1));
    all_test_data = all_test_data(test_shuffle_idx, :);
    all_test_labels = int32(all_test_labels(test_shuffle_idx));  % 转换为int32类型
    
    fprintf('最终训练集: %d 个样本\n', size(all_train_data, 1));
    fprintf('最终测试集: %d 个样本\n', size(all_test_data, 1));
    
    % ==================== 可视化样本 ====================
    figure('Name', 'JST数据集样本示例（顺序采样）', 'Position', [100, 100, 1200, 800]);
    
    % 为每个类别可视化一个样本
    for class_id = 0:2
        class_idx = find(all_train_labels == class_id, 1);
        if ~isempty(class_idx)
            subplot(3, 1, class_id+1);
            plot(all_train_data(class_idx, :));
            title(sprintf('类别 %d 样本示例 - 顺序采样', class_id));
        end
    end
    
    % 保存样本可视化图像
    saveas(gcf, fullfile(final_mat_path, 'samples_visualization_sequential.png'));
    
    % ==================== 保存为MATLAB格式 ====================
    fprintf('保存为MATLAB格式...\n');
    
    % 保存训练集
    save(fullfile(final_mat_path, 'train', 'train_dataset_sequential.mat'), 'all_train_data', 'all_train_labels');
    
    % 保存测试集
    save(fullfile(final_mat_path, 'test', 'test_dataset_sequential.mat'), 'all_test_data', 'all_test_labels');

    % ==================== 生成并执行Python脚本来转换为NPY格式 ====================
    fprintf('生成并执行Python脚本来转换为NPY格式...\n');

    % 创建临时Python脚本文件
    temp_dir = fullfile(tempdir, 'jst_temp_sequential');
    if ~exist(temp_dir, 'dir')
        mkdir(temp_dir);
    end

    py_script_path = fullfile(temp_dir, 'convert_to_numpy_sequential.py');
    fid = fopen(py_script_path, 'w');

    if fid < 0
        error('无法创建Python转换脚本: %s', py_script_path);
    end

    % 写入Python脚本内容
    fprintf(fid, '#!/usr/bin/env python\n');
    fprintf(fid, '# -*- coding: utf-8 -*-\n\n');
    fprintf(fid, '# 此脚本由MATLAB自动生成，用于将JST数据集从MAT格式转换为NPY格式（顺序采样版本）\n\n');

    fprintf(fid, 'import numpy as np\n');
    fprintf(fid, 'import scipy.io as sio\n');
    fprintf(fid, 'import os\n\n');

    fprintf(fid, '# 创建输出目录\n');
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n', strrep(fullfile(final_npy_path, 'train'), '\', '\\'));
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n\n', strrep(fullfile(final_npy_path, 'test'), '\', '\\'));

    fprintf(fid, '# 加载训练数据\n');
    fprintf(fid, 'print("加载训练数据...")\n');
    fprintf(fid, 'train_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'train', 'train_dataset_sequential.mat'), '\', '\\'));

    fprintf(fid, '# 加载测试数据\n');
    fprintf(fid, 'print("加载测试数据...")\n');
    fprintf(fid, 'test_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'test', 'test_dataset_sequential.mat'), '\', '\\'));

    fprintf(fid, '# 保存训练数据\n');
    fprintf(fid, 'print("保存训练数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", train_data["all_train_data"])\n', strrep(fullfile(final_npy_path, 'train', 'jst_data_sequential.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", train_data["all_train_labels"])\n\n', strrep(fullfile(final_npy_path, 'train', 'jst_label_sequential.npy'), '\', '\\'));

    fprintf(fid, '# 保存测试数据\n');
    fprintf(fid, 'print("保存测试数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", test_data["all_test_data"])\n', strrep(fullfile(final_npy_path, 'test', 'jst_data_sequential.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", test_data["all_test_labels"])\n\n', strrep(fullfile(final_npy_path, 'test', 'jst_label_sequential.npy'), '\', '\\'));

    fprintf(fid, 'print("转换完成！")\n');
    fclose(fid);

    % 创建说明文件
    readme_path = fullfile(final_npy_path, 'README_sequential.txt');
    fid = fopen(readme_path, 'w');
    if fid > 0
        fprintf(fid, 'JST数据集 - NPY格式数据说明（顺序采样版本）\n');
        fprintf(fid, '==========================================\n\n');
        fprintf(fid, '数据格式说明:\n');
        fprintf(fid, '- NPY文件格式: \n');
        fprintf(fid, '  * 训练数据: jst_data_sequential.npy (样本矩阵), jst_label_sequential.npy (整数类型标签)\n');
        fprintf(fid, '  * 测试数据: jst_data_sequential.npy (样本矩阵), jst_label_sequential.npy (整数类型标签)\n\n');
        fprintf(fid, '标签说明:\n');
        fprintf(fid, '- 0: 正常\n');
        fprintf(fid, '- 1: 弱水力振动\n');
        fprintf(fid, '- 2: 强水力振动\n\n');
        fprintf(fid, '样本数量:\n');
        fprintf(fid, '- 训练集: %d 个样本\n', size(all_train_data, 1));
        fprintf(fid, '- 测试集: %d 个样本\n\n', size(all_test_data, 1));
        fprintf(fid, '采样方法:\n');
        fprintf(fid, '- 采用顺序采样方式，按设定的重叠长度依次提取样本\n');
        fprintf(fid, '- 训练样本从信号的前%.0f%%长度选取，测试样本从后%.0f%%长度选取\n', train_ratio*100, (1-train_ratio)*100);
        fprintf(fid, '- 每个样本包含 %d 个数据点，相邻样本重叠 %d 个数据点\n', sample_length, overlap_length);
        fprintf(fid, '- 数据位置: %d\n', Location_s);
        fclose(fid);
    end

    % 执行Python脚本
    fprintf('正在执行Python脚本以生成NPY格式数据文件...\n');

    try
        % 判断系统平台
        if ispc
            % Windows系统
            [status, cmdout] = system(['python "', py_script_path, '"']);
        else
            % Linux/Mac系统
            [status, cmdout] = system(['python3 "', py_script_path, '"']);
        end

        if status == 0
            fprintf('Python脚本执行成功！\n%s\n', cmdout);
        else
            fprintf('Python脚本执行失败！错误码: %d\n%s\n', status, cmdout);
            fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
            fprintf('python "%s"\n', py_script_path);
        end
    catch e
        fprintf('执行Python脚本时出错: %s\n', e.message);
        fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
        fprintf('python "%s"\n', py_script_path);
    end

    % 尝试删除临时Python脚本
    try
        delete(py_script_path);
        rmdir(temp_dir);
        fprintf('临时Python脚本已删除\n');
    catch
        fprintf('无法删除临时Python脚本: %s\n', py_script_path);
    end

    fprintf('JST数据集预处理完成（顺序采样版本）!\n');
end

function samples = extract_sequential_samples_jst(signal, sample_length, overlap_length, max_samples)
    % 从信号中按顺序提取固定长度的样本，支持重叠采样（JST版本）
    % 输入:
    %   signal: 原始信号
    %   sample_length: 每个样本的长度
    %   overlap_length: 相邻样本的重叠长度
    %   max_samples: 最大提取样本数量（如果为0或负数，则提取所有可能的样本）
    % 输出:
    %   samples: 提取的样本矩阵，每行一个样本

    signal_length = length(signal);
    step_size = sample_length - overlap_length;  % 步长

    % 检查信号长度是否足够
    if signal_length < sample_length
        error('信号长度(%d)不足以提取长度为%d的样本', signal_length, sample_length);
    end

    % 检查步长是否合理
    if step_size <= 0
        error('重叠长度(%d)不能大于等于样本长度(%d)', overlap_length, sample_length);
    end

    % 计算可以提取的最大样本数
    max_possible_samples = floor((signal_length - sample_length) / step_size) + 1;

    % 确定实际提取的样本数
    if max_samples <= 0 || max_samples > max_possible_samples
        num_samples = max_possible_samples;
    else
        num_samples = max_samples;
    end

    % 初始化样本矩阵
    samples = zeros(num_samples, sample_length);

    % 按顺序提取样本
    for i = 1:num_samples
        start_idx = (i - 1) * step_size + 1;
        end_idx = start_idx + sample_length - 1;
        samples(i, :) = signal(start_idx:end_idx);
    end

    fprintf('    从长度为%d的信号中提取了%d个样本（步长=%d，重叠=%d）\n', ...
        signal_length, num_samples, step_size, overlap_length);
end
