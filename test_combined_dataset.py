#!/usr/bin/env python3
"""
测试组合数据集功能的脚本
验证新的数据流程是否正确工作
"""

import os
import sys
import numpy as np
import torch
from torch.utils.data import DataLoader

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.utils import load_config, setup_logging, set_seed, get_device
from common.data_loader import VibrationDataLoader, CombinedDataLoader
from common.performance_manager import apply_performance_config
from main import save_combined_dataset, adjust_healthy_samples_for_balance

def test_data_flow():
    """测试完整的数据流程"""
    print("=" * 60)
    print("测试组合数据集功能")
    print("=" * 60)
    
    # 加载配置
    config = load_config('config.yaml')

    # 应用性能配置
    config = apply_performance_config(config)

    # 设置日志
    log_dir = config['system']['save']['logs_dir']
    logger = setup_logging(log_dir, 'test_combined_dataset')
    
    # 设置随机种子
    set_seed(config['system']['seed'])
    
    # 获取设备
    device = get_device(config['system']['device'])
    
    print(f"数据集: {config['dataset']['name']}")
    print(f"增强方法: {config['augmentation']['method']}")
    
    # 1. 加载原始数据
    print("\n1. 加载原始数据...")
    data_loader = VibrationDataLoader(config)
    train_loader, test_loader, data_info = data_loader.load_data()
    
    # 获取完整的训练数据
    full_train_data = []
    full_train_labels = []
    for data, labels in train_loader:
        full_train_data.append(data.numpy())
        full_train_labels.append(labels.numpy())
    full_train_data = np.concatenate(full_train_data, axis=0)
    full_train_labels = np.concatenate(full_train_labels, axis=0)
    
    print(f"原始训练数据: {full_train_data.shape}")
    print(f"原始训练标签: {full_train_labels.shape}")
    print(f"类别分布: {dict(zip(*np.unique(full_train_labels, return_counts=True)))}")
    
    # 2. 模拟生成数据
    print("\n2. 模拟生成增强数据...")
    num_generated_per_class = config['augmentation']['num_generated_per_class']
    if isinstance(num_generated_per_class, list):
        num_generated_per_class = num_generated_per_class[0]
    
    num_classes = config['dataset']['datasets'][config['dataset']['name']]['num_classes']
    signal_length = config['dataset']['data_loading']['signal_length']
    
    # 模拟生成数据（随机噪声）
    generated_data = []
    generated_labels = []
    
    generate_fault_only = config['augmentation'].get('generate_fault_only', False)
    healthy_label = config['dataset']['data_loading'].get('healthy_samples', {}).get('healthy_label', 0)
    
    for class_id in range(num_classes):
        if generate_fault_only and class_id == healthy_label:
            print(f"跳过健康样本类别 {class_id}")
            continue
            
        # 生成随机数据（模拟）
        class_data = np.random.randn(num_generated_per_class, signal_length) * 0.1
        class_labels = np.full(num_generated_per_class, class_id)
        
        generated_data.append(class_data)
        generated_labels.append(class_labels)
    
    if generated_data:
        generated_data = np.concatenate(generated_data, axis=0)
        generated_labels = np.concatenate(generated_labels, axis=0)
    else:
        generated_data = np.array([])
        generated_labels = np.array([])
    
    print(f"生成数据: {generated_data.shape}")
    print(f"生成标签: {generated_labels.shape}")
    if len(generated_data) > 0:
        print(f"生成数据类别分布: {dict(zip(*np.unique(generated_labels, return_counts=True)))}")
    
    # 3. 合并数据
    print("\n3. 合并真实数据和生成数据...")
    if len(generated_data) == 0:
        combined_data = full_train_data
        combined_labels = full_train_labels
    else:
        combined_data = np.concatenate([full_train_data, generated_data], axis=0)
        combined_labels = np.concatenate([full_train_labels, generated_labels], axis=0)
    
    print(f"合并前数据: {combined_data.shape}")
    
    # 4. 健康样本平衡调整
    print("\n4. 健康样本平衡调整...")
    combined_data, combined_labels = adjust_healthy_samples_for_balance(
        combined_data, combined_labels, full_train_data, full_train_labels, config
    )
    
    print(f"调整后数据: {combined_data.shape}")
    print(f"最终类别分布: {dict(zip(*np.unique(combined_labels, return_counts=True)))}")
    
    # 5. 保存组合数据集
    print("\n5. 保存组合数据集...")
    combined_data_info = save_combined_dataset(combined_data, combined_labels, config)
    print(f"保存路径: {combined_data_info['save_dir']}")
    
    # 6. 测试组合数据加载器
    print("\n6. 测试组合数据加载器...")
    combined_loader = CombinedDataLoader(config)
    
    # 加载保存的组合数据
    loaded_data, loaded_labels = combined_loader.load_combined_dataset()
    print(f"加载的数据: {loaded_data.shape}")
    print(f"加载的标签: {loaded_labels.shape}")
    
    # 验证数据一致性
    data_match = np.array_equal(combined_data, loaded_data)
    labels_match = np.array_equal(combined_labels, loaded_labels)
    print(f"数据一致性检查: 数据={data_match}, 标签={labels_match}")
    
    # 7. 创建数据加载器
    print("\n7. 创建训练/验证/测试数据加载器...")
    
    # 获取测试数据
    test_data = []
    test_labels = []
    for data, labels in test_loader:
        test_data.append(data.numpy())
        test_labels.append(labels.numpy())
    test_data = np.concatenate(test_data, axis=0)
    test_labels = np.concatenate(test_labels, axis=0)
    
    train_val_split = config['dataset']['data_loading']['train_val_split']
    train_loader_new, val_loader_new, test_loader_new = combined_loader.create_data_loaders(
        loaded_data, loaded_labels, test_data, test_labels, train_val_split
    )
    
    print(f"新训练加载器: {len(train_loader_new.dataset)} 样本")
    print(f"新验证加载器: {len(val_loader_new.dataset)} 样本")
    print(f"新测试加载器: {len(test_loader_new.dataset)} 样本")
    
    # 8. 验证数据加载器
    print("\n8. 验证数据加载器...")
    train_batch = next(iter(train_loader_new))
    val_batch = next(iter(val_loader_new))
    test_batch = next(iter(test_loader_new))
    
    print(f"训练批次: 数据{train_batch[0].shape}, 标签{train_batch[1].shape}")
    print(f"验证批次: 数据{val_batch[0].shape}, 标签{val_batch[1].shape}")
    print(f"测试批次: 数据{test_batch[0].shape}, 标签{test_batch[1].shape}")
    
    print("\n✅ 组合数据集功能测试完成！")
    print("=" * 60)
    
    return {
        'original_data_shape': full_train_data.shape,
        'generated_data_shape': generated_data.shape,
        'combined_data_shape': combined_data.shape,
        'train_samples': len(train_loader_new.dataset),
        'val_samples': len(val_loader_new.dataset),
        'test_samples': len(test_loader_new.dataset),
        'data_consistency': data_match and labels_match
    }

if __name__ == "__main__":
    try:
        results = test_data_flow()
        print(f"\n测试结果: {results}")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
