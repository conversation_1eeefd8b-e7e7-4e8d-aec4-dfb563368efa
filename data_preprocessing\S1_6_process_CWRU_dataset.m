% CWRU数据集预处理程序
% 处理CWRU轴承故障数据集，数据分割并保存为MATLAB和NPY格式
% 原样本包括四种状态：正常状态(0)、内圈故障(1)、滚动体故障(2)、外圈故障(3)

% 清空工作区和命令窗口
clear;
clc;

try
    % 开始计时
    tic;
    
    % 调用处理函数
    process_CWRU_dataset();
    
    % 结束计时
    elapsed_time = toc;
    fprintf('处理完成! 耗时: %.2f 秒\n', elapsed_time);
catch e
    % 捕获并显示错误
    fprintf('处理过程中发生错误:\n');
    disp(e.message);
    if ~isempty(e.stack)
        fprintf('错误位置: %s (行 %d)\n', e.stack(1).name, e.stack(1).line);
    end
end

% ================== 主处理函数 ==================
function process_CWRU_dataset()
    % ==================== 参数设置 ====================
    sample_length = 1024;           % 每个样本的长度（采样点数）
    train_samples_per_class = 200;  % 每个类别生成的训练样本数量
    test_samples_per_class = 200;   % 每个类别生成的测试样本数量
    train_ratio = 0.7;              % 训练数据从前70%选择，测试数据从后30%选择
    random_seed = 42;               % 随机种子，确保结果可重现
    
    % ==================== 路径设置 ====================
    % 获取脚本所在目录的上级目录作为基础路径
    script_dir = fileparts(mfilename('fullpath'));
    base_dir = fileparts(script_dir);  % 上级目录
    
    % 使用基础路径构建完整路径
    ori_path = fullfile(base_dir, 'dataset', 'CWRU', 'ori_mat');      % 原始数据目录
    final_mat_path = fullfile(base_dir, 'dataset', 'CWRU', 'used_mat');  % 最终mat文件保存目录
    final_npy_path = fullfile(base_dir, 'dataset', 'CWRU', 'used_npy');  % 最终npy文件保存目录
    
    fprintf('使用以下路径:\n');
    fprintf('原始数据目录: %s\n', ori_path);
    fprintf('Mat文件保存目录: %s\n', final_mat_path);
    fprintf('NPY文件保存目录: %s\n', final_npy_path);
    
    % 检查原始数据目录是否存在
    if ~exist(ori_path, 'dir')
        error('原始数据目录不存在: %s', ori_path);
    end
    
    fprintf('开始处理CWRU数据集...\n');
    
    % ==================== 创建保存目录 ====================
    if ~exist(fullfile(final_mat_path, 'train'), 'dir')
        mkdir(fullfile(final_mat_path, 'train'));
    end
    if ~exist(fullfile(final_mat_path, 'test'), 'dir')
        mkdir(fullfile(final_mat_path, 'test'));
    end
    if ~exist(fullfile(final_npy_path, 'train'), 'dir')
        mkdir(fullfile(final_npy_path, 'train'));
    end
    if ~exist(fullfile(final_npy_path, 'test'), 'dir')
        mkdir(fullfile(final_npy_path, 'test'));
    end
    
    % ==================== 创建可视化窗口 ====================
    figure('Name', 'CWRU数据集样本示例', 'Position', [100, 100, 1200, 800]);
    
    % ==================== 读取原始数据 ====================
    fprintf('读取原始数据文件...\n');
    
    % 用于存储所有类别的原始信号和标签
    class_signals = cell(4, 1);  % 存储4个类别的原始信号
    class_names = {'正常', '内圈故障', '滚动体故障', '外圈故障'};
    
    % 处理四种不同类别的数据
    for class_id = 0:3
        fprintf('处理类别 %d (%s) 的数据...\n', class_id, class_names{class_id+1});
        
        % 构建类别数据路径
        class_path = fullfile(ori_path, num2str(class_id));
        
        % 检查类别目录是否存在
        if ~exist(class_path, 'dir')
            warning('类别 %d 的目录不存在: %s', class_id, class_path);
            continue;
        end
        
        % 查找所有mat文件
        mat_files = dir(fullfile(class_path, '*.mat'));
        if isempty(mat_files)
            warning('类别 %d 的目录中未找到.mat文件: %s', class_id, class_path);
            continue;
        end
        
        % 加载第一个mat文件
        mat_file_path = fullfile(class_path, mat_files(1).name);
        fprintf('加载文件: %s\n', mat_file_path);
        try
            data = load(mat_file_path);
        catch e
            warning('无法加载类别 %d 的数据文件: %s\n错误: %s', class_id, mat_file_path, e.message);
            continue;
        end
        
        % 提取X099_FE_time数据
        try
            % 直接寻找包含FE_time的字段
            field_names = fieldnames(data);
            fe_fields = field_names(contains(field_names, 'FE_time'));
            
            if ~isempty(fe_fields)
                fprintf('使用字段: %s\n', fe_fields{1});
                signal = data.(fe_fields{1});
                
                % 存储类别信号
                class_signals{class_id+1} = signal;
                
                fprintf('类别 %d: 提取了 %d 个数据点\n', class_id, length(signal));
            else
                % 如果没有找到FE_time字段，打印所有可用字段
                fprintf('未找到包含FE_time的字段。可用字段:\n');
                for i = 1:length(field_names)
                    fprintf('  %s\n', field_names{i});
                end
                warning('无法找到适合的数据字段');
            end
        catch e
            warning('处理类别 %d 的数据时出错: %s', class_id, e.message);
            continue;
        end
    end
    
    % 检查是否成功提取了样本
    if all(cellfun(@isempty, class_signals))
        error('未能成功提取任何样本。请检查数据文件和处理逻辑。');
    end
    
    % ==================== 从信号中随机选择样本 ====================
    fprintf('从信号中随机选择样本...\n');
    
    % 设置随机种子以确保结果可重现
    rng(random_seed);
    
    % 准备存储训练和测试样本
    all_train_data = [];
    all_train_labels = [];
    all_test_data = [];
    all_test_labels = [];
    
    % 按类别处理
    for class_id = 0:3
        fprintf('处理类别 %d (%s) 的样本...\n', class_id, class_names{class_id+1});
        
        % 获取当前类别的信号
        signal = class_signals{class_id+1};
        
        if isempty(signal)
            warning('类别 %d 的信号为空，跳过处理', class_id);
            continue;
        end
        
        % 确保信号长度足够
        if length(signal) < sample_length
            warning('类别 %d 的信号长度不足 (%d < %d)，跳过处理', ...
                class_id, length(signal), sample_length);
            continue;
        end
        
        % 划分训练和测试区域
        train_end_idx = floor(length(signal) * train_ratio);
        train_signal = signal(1:train_end_idx);
        test_signal = signal(train_end_idx+1:end);
        
        % 从信号中随机提取训练样本
        train_samples = extract_random_samples(train_signal, sample_length, train_samples_per_class);
        train_labels = ones(size(train_samples, 1), 1) * class_id;
        
        % 从信号中随机提取测试样本
        test_samples = extract_random_samples(test_signal, sample_length, test_samples_per_class);
        test_labels = ones(size(test_samples, 1), 1) * class_id;
        
        % 添加到总样本集
        all_train_data = [all_train_data; train_samples];
        all_train_labels = [all_train_labels; train_labels];
        all_test_data = [all_test_data; test_samples];
        all_test_labels = [all_test_labels; test_labels];
        
        fprintf('类别 %d: 生成了 %d 个训练样本和 %d 个测试样本\n', ...
            class_id, size(train_samples, 1), size(test_samples, 1));
        
        % 可视化样本
        subplot(4, 1, class_id+1);
        plot(train_samples(1, :));
        title(sprintf('类别 %d (%s) 样本示例', class_id, class_names{class_id+1}));
    end
    
    % 保存图像
    saveas(gcf, fullfile(final_mat_path, 'sample_visualization.png'));
    fprintf('样本可视化已保存到: %s\n', fullfile(final_mat_path, 'sample_visualization.png'));
    
    % ==================== 保存为MATLAB格式 ====================
    fprintf('保存为MATLAB格式...\n');
    
    % 保存训练集
    save(fullfile(final_mat_path, 'train', 'train_dataset.mat'), 'all_train_data', 'all_train_labels');
    
    % 保存测试集
    save(fullfile(final_mat_path, 'test', 'test_dataset.mat'), 'all_test_data', 'all_test_labels');
    
    % ==================== 生成并执行Python脚本来转换为NPY格式 ====================
    fprintf('生成并执行Python脚本来转换为NPY格式...\n');
    
    % 创建临时Python脚本文件
    temp_dir = fullfile(tempdir, 'cwru_temp');
    if ~exist(temp_dir, 'dir')
        mkdir(temp_dir);
    end
    
    py_script_path = fullfile(temp_dir, 'convert_to_numpy.py');
    fid = fopen(py_script_path, 'w');
    
    if fid < 0
        error('无法创建Python转换脚本: %s', py_script_path);
    end
    
    % 写入Python脚本内容
    fprintf(fid, '#!/usr/bin/env python\n');
    fprintf(fid, '# -*- coding: utf-8 -*-\n\n');
    fprintf(fid, '# 此脚本由MATLAB自动生成，用于将CWRU数据集从MAT格式转换为NPY格式\n\n');
    
    fprintf(fid, 'import numpy as np\n');
    fprintf(fid, 'import scipy.io as sio\n');
    fprintf(fid, 'import os\n\n');
    
    fprintf(fid, '# 创建输出目录\n');
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n', strrep(fullfile(final_npy_path, 'train'), '\', '\\'));
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n\n', strrep(fullfile(final_npy_path, 'test'), '\', '\\'));
    
    fprintf(fid, '# 加载训练数据\n');
    fprintf(fid, 'print("加载训练数据...")\n');
    fprintf(fid, 'train_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'train', 'train_dataset.mat'), '\', '\\'));
    
    fprintf(fid, '# 加载测试数据\n');
    fprintf(fid, 'print("加载测试数据...")\n');
    fprintf(fid, 'test_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'test', 'test_dataset.mat'), '\', '\\'));
    
    fprintf(fid, '# 保存训练数据\n');
    fprintf(fid, 'print("保存训练数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", train_data["all_train_data"])\n', strrep(fullfile(final_npy_path, 'train', 'cwru_data.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", train_data["all_train_labels"])\n\n', strrep(fullfile(final_npy_path, 'train', 'cwru_label.npy'), '\', '\\'));
    
    fprintf(fid, '# 保存测试数据\n');
    fprintf(fid, 'print("保存测试数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", test_data["all_test_data"])\n', strrep(fullfile(final_npy_path, 'test', 'cwru_data.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", test_data["all_test_labels"])\n\n', strrep(fullfile(final_npy_path, 'test', 'cwru_label.npy'), '\', '\\'));
    
    fprintf(fid, 'print("转换完成！")\n');
    fclose(fid);
    
    % 创建说明文件
    readme_path = fullfile(final_npy_path, 'README.txt');
    fid = fopen(readme_path, 'w');
    if fid > 0
        fprintf(fid, 'CWRU数据集 - NPY格式数据说明\n');
        fprintf(fid, '==============================\n\n');
        fprintf(fid, '数据格式说明:\n');
        fprintf(fid, '- NPY文件格式: \n');
        fprintf(fid, '  * 训练数据: cwru_data.npy (样本矩阵), cwru_label.npy (整数类型标签)\n');
        fprintf(fid, '  * 测试数据: cwru_data.npy (样本矩阵), cwru_label.npy (整数类型标签)\n\n');
        fprintf(fid, '标签说明:\n');
        fprintf(fid, '- 0: 正常\n');
        fprintf(fid, '- 1: 内圈故障\n');
        fprintf(fid, '- 2: 滚动体故障\n');
        fprintf(fid, '- 3: 外圈故障\n\n');
        fprintf(fid, '样本数量:\n');
        fprintf(fid, '- 训练集: 每个类别 %d 个样本 (总计 %d 个样本)\n', train_samples_per_class, train_samples_per_class*4);
        fprintf(fid, '- 测试集: 每个类别 %d 个样本 (总计 %d 个样本)\n\n', test_samples_per_class, test_samples_per_class*4);
        fprintf(fid, '采样方法:\n');
        fprintf(fid, '- 从每个类别的前70%%用于训练样本随机提取，后30%%用于测试样本随机提取\n');
        fprintf(fid, '- 每个样本包含 %d 个数据点\n', sample_length);
        fclose(fid);
    end
    
    % 执行Python脚本
    fprintf('正在执行Python脚本以生成NPY格式数据文件...\n');
    
    try
        % 判断系统平台
        if ispc
            % Windows系统
            [status, cmdout] = system(['python "', py_script_path, '"']);
        else
            % Linux/Mac系统
            [status, cmdout] = system(['python3 "', py_script_path, '"']);
        end
        
        if status == 0
            fprintf('Python脚本执行成功！\n%s\n', cmdout);
        else
            fprintf('Python脚本执行失败！错误码: %d\n%s\n', status, cmdout);
            fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
            fprintf('python "%s"\n', py_script_path);
        end
    catch e
        fprintf('执行Python脚本时出错: %s\n', e.message);
        fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
        fprintf('python "%s"\n', py_script_path);
    end
    
    % 尝试删除临时Python脚本
    try
        delete(py_script_path);
        rmdir(temp_dir);
        fprintf('临时Python脚本已删除\n');
    catch
        fprintf('无法删除临时Python脚本: %s\n', py_script_path);
    end
    
    fprintf('CWRU数据集预处理完成!\n');
end

function samples = extract_random_samples(signal, sample_length, num_samples)
    % 从信号中随机提取固定长度的样本
    % 输入:
    %   signal: 原始信号
    %   sample_length: 每个样本的长度
    %   num_samples: 需要提取的样本数量
    % 输出:
    %   samples: 提取的样本矩阵，每行一个样本
    
    signal_length = length(signal);
    max_start_idx = signal_length - sample_length + 1;
    
    % 检查信号长度是否足够
    if max_start_idx <= 0
        error('信号长度不足以提取长度为%d的样本', sample_length);
    end
    
    % 初始化样本矩阵
    samples = zeros(num_samples, sample_length);
    
    % 随机选择起始位置并提取样本
    for i = 1:num_samples
        start_idx = randi(max_start_idx);
        end_idx = start_idx + sample_length - 1;
        samples(i, :) = signal(start_idx:end_idx);
    end
end 