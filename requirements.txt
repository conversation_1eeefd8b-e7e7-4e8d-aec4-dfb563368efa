# 深度学习框架
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# 数据处理
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0
scikit-learn>=1.0.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# 配置文件处理
PyYAML>=6.0
omegaconf>=2.1.0

# 日志和监控
tensorboard>=2.8.0
wandb>=0.12.0
tqdm>=4.62.0

# 图像处理和评估指标
Pillow>=8.3.0
opencv-python>=4.5.0
torchmetrics>=0.7.0

# FID计算
pytorch-fid>=0.3.0

# 模型复杂度计算
thop>=0.1.0
ptflops>=0.6.0

# 降维和聚类
scikit-learn>=1.0.0
umap-learn>=0.5.0

# 其他工具
einops>=0.4.0
timm>=0.6.0
transformers>=4.20.0

# 开发工具
jupyter>=1.0.0
ipykernel>=6.0.0
black>=22.0.0
flake8>=4.0.0
