# Data Augmentation Fault Diagnosis of Rolling Machinery Using Condition Denoising Diffusion Probabilistic Model and Improved CNN


# Abstract:
Imbalanced data present a significant challenge in intelligent fault diagnosis due to limited sample availability,

Document Sections

1. Introduction

II. Preliminary Knowledge

and various generative models have been proposed for data augmentation. However, these training processes are often complex, prone to model collapse, and fail to improve the classifiers ability to categorize fault signals. Consequently, a bearing fault diagnosis method based on condition denoising diffusion probabilistic model (DDPM) and improved convolution neural network (CNN) is proposed to address class-imbalanced. First, the condition DDPM is introduced to generate high-quality and diverse vibration synthetic data through accurate physical simulation. Second, the synthetic data’ s effectiveness is assessed using the proposed improved CNN which offers enhanced flexibility in perception. Third, the GAN-train/GAN-test is used to provide a comprehensive evaluation method for synthetic data and indicate the diversity and similarity of synthetic sample. Finally, two machinery datasets, five classifier models and seven data augmentation methods are compared under varying imbalance ratios of 1:2, 1:4, and 1:10. These results confirm the sample generation capabilities of condition DDPM and the high classification performance of improved CNN.

II. Proposed Methodology

IV. Fault Recognition of

Rolling Bearing

V. Ablation Studies



### SECTION I. Introduction


In modern industries, rolling bearings are indispensable components in various mechanical systems [1] These bearings are typically subject to harsh operating conditions, which lead to degradation or malfunctior [2]. Consequently, implementing effective fault diagnosis is essential for ensuring the reliable daily operation and maintenance of machines [3]. Traditional fault diagnosis, which requires signal processing to extract features for machine-learning analysis, struggles to maintain accuracy and effectiveness in environments where machine data are growing exponentially [4], [5]

With the increase in computer arithmetic and the development of deep learning algorithms, intelligent fault diagnosis is proposed to fully process and analyze data, providing accurate diagnostic results [6]. The intelligent algorithms capitalize on the inherent relevant features of signals and eliminate the need for manual feature selection in machine learning [Z]. Recent research has extensively demonstrated that the superior performance of deep learning-based techniques. For instance, Zhang et al. [8] proposed a novel fault diagnosis method based on local binary temporal convolutional neural network (LBTCNN) to address the issue of overfitting due to limited faulty data. Niu et al. [9] adopted a channel attention module and a nonlocal attention module to improve the feature learning ability of deep residual convolution neural network (CNN), utilizing information fusion from multiple sensor data. Chen et al. [1o] proposed a multigrained hybrid neural network using short-time Fourier transform for time-frequency image conversion and incorporating multigrained feature representation and depthwise wave blocks to improve fault diagnosis accuracy and robustness

However, the aforementioned structure of CNNs is complex, with multiple modules designed to enhance feature learning ability. Additionally, it assumes that the training set contains sufficient data for different categories, which is unrealistic in industrial applications. In engineering scenarios, the number of healthy class samples outweighs those from other fault classes, leading to the model classifying all test samples into the dominant class [1]. To address this issue, algorithm-based and data-based are two main method to solve imbalanced fault diagnosis [12]. The former methods involve modifying the learning algorithms to better handle class imbalance. However, these methods are often customized for specific problems and tend to lack portability [13]. In contrast, methods that address the imbalance by modifying the dataset itself are generally more versatile and widely applicable. From a data perspective, techniques such as data oversampling and data generation are primarily used for data augmentation [14]. The synthetic minority oversampling technique (SMOTE) serves as a foundational approach to data oversampling. Building on this technique, subsequenf algorithms such as adaptive synthetic sampling (ADASYN) [15] and SMOTEENN [16], which integrates both oversampling and undersampling methods, have been developed to enhance its effectiveness further However, SMOTE does not fully account for the true data distribution, which may lead to a potential loss of information in the dataset

Thus, data augmentation techniques based on generative models have gradually become a primary method for addressing data imbalance problems [17]. The mainstream generative models commonly used for fault diagnosis are variational autoencoders (VAEs) and generative adversarial networks (GANs). VAEs take a probabilistic approach, where the encoder outputs a latent space distribution, allowing sampling during decoding to generate new data instances. Zhao et al. [18] proposed the normalized conditional variational auto-encoder with adaptive focal loss for mechanical fault diagnosis under class-imbalanced data conditions However, since VAE training relies on a hypothetical loss function and Kullback-Leibler divergence to approximate the true distribution, its generated outputs can sometimes appear overly smooth and less realistic compared. In contrast, GAN can learn the data distribution characteristics of input samples through generator and discriminator, which allows GAN to generate realistic samples [1q]. Huo et al. [2Q] developed a new model combining residual mixed self-attention and Wasserstein GAN with a 1-D CNN for industrial bearing fault diagnosis, effectively addressing data imbalance and improving classification performance. Dai et al. [21] developed a categorical feature GAN to generate synthetic samples for balancing dataset, improving sample quality and diversity through integrated autoencoder and ACGAN technologies. Sun et al. [22] proposed a novel approach to cross-domain data augmentation based on the envelope order spectrum utilizing an enhanced variational autoencoder generative adversarial network (VAEGAN) to address the challenge of insufficient labeled training data. Although these data augmentation-based methods have indeed. improved the diagnosis accuracy to some extent, their reliance on game theory principles, which can lead to unstable model training and potential model collapse. Approaches such as the Wasserstein GAN and its variant with WGAN-GP have been developed to address issues like mode collapse, they still fall short in terms of diversity, and need to carefully selected hyperparameters and regularizers. Furthermore, the intermediate processes of these methods are not clearly articulated through mathematical formulation

Given the limitations of existing generative models, the diffusion model [23] presents a novel generative approach characterized by more intuitive mathematical principles and a clear probabilistic frame generating high-quality and diverse sample distributions, despite requiring significant computat resources. Furthermore, it incorporate parameterized Markov processes to enhance computational efficiency and improve the interpretability of the results. Several studies have been proposed to assess the effectivenes of diffusion model. Yang et al. [24] introduced a diffusion model-based method for fault diagnosis that handles imbalanced data by generating stable, high-quality samples and proposed a comprehensive evaluation framework for thorough performance assessment. Zhang et al. [25] introduced the interpretable vector quantization-guided latent denoising diffusion probabilistic model (DDPM) for machinery fault diagnosis with limited data availability

Although DDPM can be applied to fault diagnosis with unbalanced samples, it often results in the underutilization of labeling information. The training process typically generates samples based on fault types in separate sessions, which does not fully exploit the interrelations of labeling information across different fault types. Furthermore, most generative models often overlook the performance of classifier model. Thus, this article introduces condition guidance to traditional DDPM, leading to more accurate model training and more effective sample generation process. Additionally, the integration of deformable concepts with. traditional CNN is proposed to improve the model's feature learning capabilities. Ultimately, a comprehensive approach to generate data quality assessments is presented. The contributions of this article can be summarized as follows

1) The condition DDPM is presented to address imbalanced data in fault diagnosis. It incorporates label information during the training and sample generation procedures, generating more realistic and diverse synthetic data with the guidance

2) An improved CNN with deformable convolution is proposed, which enhances the perceptual range by calculating feature offsets and increasing the number of channels. Additionally, the adaptive pooling i

incorporated to reduce interference from irrelevant features. The proposed classifier model fully leverages the inherent distinguishing features of vibration signals 3) A comprehensive assessment of the synthetic data is conducted on two machinery datasets, five classifier models, and seven data augmentation methods under varying degree of unbalanced. The results demonstrate that the proposed model can generate high-quality and diversity samples, achieving accurate fault diagnosis

The article is structured as follows. Section II covers the basics of the proposed data augmentation and classifier model. Section II details the model’s structure and parameters. Section IV describes the experimental setup used to test the method’s effectiveness. Section V presents the ablation experiment and the visual analysis of the generated sample. Finally, Section VI presents the conclusion and future directions

## SECTION II. Preliminary Knowledge

## A.Condition DDPM

DDPM is inspired by the physics principle of nonequilibrium thermodynamics. It consists two main parts: a forward phase that adds Gaussian noise to the data, and a reverse phase that denoises and reconstructs the original structure [26]. However, traditional DDPM lacks control over the type of data generated, making if difficult to handle multiple fault types effectively. Therefore, the condition DDPM [2z] was proposed embedding label information into the model training procedure to generate accurate samples

The forward propagation is consistent with DDPM, and this transformation is achieved by defining a Marko chain $q$ where each step t relies solely on the current data state and a predetermined variance schedule $\beta$ By controlling the variance of the noise added at each step, the signal is gradually transformed into a latent representation $x_1,\boldsymbol{x}_2,\ldots,\boldsymbol{x}_T$ that is eventually indistinguishable from pure noise

Given a data distribution $\dot{x}_0\sim q(x_0)$, the process can be expressed as follows

$$\begin{aligned}
q\left(x_{t}|x_{t-1}\right)& =\mathcal{N}\left(x_{t};\sqrt{1-\beta_{t}}x_{t-1},\beta_{t}\mathbf{I}\right) \\
q\left(\mathbf{x}_{1},\ldots,\mathbf{x}_{T}|\mathbf{x}_{0}\right)& =\prod_{t=1}^Tq\left(\mathbf{x}_t|\mathbf{x}_{t-1}\right) 
\end{aligned}$$

## View Source 


where $xt$ is the state of data at time t, $\mathcal{N}(\cdot)$ is Gaussian distribution at each time step with variar $\beta_{t}\in\left(0,1\right),T$ is the number of diffusion steps, I is the identity matrix, matching the dimensions β.

With a reparameterization trick: $\alpha_t=1-\beta_t$ and = , αi , (1) can be rewrite ar

$$\begin{aligned}
x_{\mathrm{t}}\sim q(x_{t}|x_{0})& =\mathcal{N}\left(x_{t};\sqrt{\overline{\alpha}_{t}}x_{0},(1-\overline{\alpha}_{t})\:\mathbf{I}\right) \\
\text{Tt}& =\sqrt{\overline{\alpha}_{t}}x_{0}+\sqrt{1-\overline{\alpha}_{t}}\epsilon 
\end{aligned}$$

## View Source

where $\epsilon \in \mathcal{N} ( 0, \mathbf{I} )$ .

The reverse diffusion process generates data from the latent space 27 and counteract the previously added noise. However, the true distribution $q(\mathbf{x}_{t-1}|\mathbf{x}_t,y)$ is typically unknown and computationally infeasible. So a neural network PVe is utilized to approximate the distribution by $p_0(\mathbf{x}_{t-1}|\mathbf{x}_t,y)$ , allowing the model to focus on parameterizing just the mean $\mu_{\theta}(x_{t},t,y)$ and variance $\sum_{\theta}(x_{t},t,y)$ to perform reverse diffusion. This allows for recreation of a data point reflective of original distribution from noise-infused latent state

$$p_\theta\left(x_{t-1}|x_t,y\right)=\mathcal{N}\left(x_{t-1};\mu_\theta\left(x_t,t,y\right),\Sigma_\theta\left(x_t,t,y\right)\right).$$

View Source

Based on the Markov chain and Bayesian formula, $q(x_{t-1}|x_t)$ can be reformulated to include ro

$$q\left(x_{t-1}|x_t,x_0\right)=N\left(x_{t-1};\tilde{\mu}\left(x_t,x_0\right),\tilde{\beta}_tI\right)$$

 View Source 0

where

$$\begin{aligned}
\bar{\beta}_{t}& =\frac{1-\overline{\alpha}_{t-1}}{1-\overline{\alpha}_{t}}\beta_{t} \\
\tilde{\mu}_{t}\left(x_{t},x_{0}\right)& =\frac{\sqrt{\overline{\alpha}_{t-1}}\beta_{t}}{1-\overline{\alpha}_{t}}x_{0}+\frac{\sqrt{\alpha_{t}}\left(1-\overline{\alpha}_{t-1}\right)}{1-\overline{\alpha}_{t}}x_{t} \\
x_{0}& =\frac{1}{\sqrt{\overline{\alpha}_{t}}}\big(x_{t}-\sqrt{1-\overline{\alpha}_{t}}\:\epsilon_{t}\big)\:. 
\end{aligned}$$

### View Source 

Thus, the model can be trained by the combination of $P$ and 4 via variational lower bound on the negative log likelihood

$$L_{VLB}=\mathbb{E}_{q(x_{0T})}\left[\log\frac{q\left(X_{1:T}|X_{0}\right)}{p_{\theta}\left(X_{0:T}\right)}\right].$$

## View Source 

Considering $L_{VLB}$ is analytically computable, a neural network can be employed to estimate, which serves as an approximation of. and bv integrating (5)(8).(o) can be simplified tc

$$L_t^\text{simple}=\mathbb{E}_{x_0,t,y,\epsilon}\left[\|\epsilon-\epsilon_\theta\left(\sqrt{\overline{\alpha}_t}x_0+\sqrt{1-\overline{\alpha}_t}\epsilon,t,y\right)\|^2\right]$$

## View Source 

where t is the uniform between 1 and T, y is the label information


During the training period, both conditional and unconditional information are trained together. The. conditional model is trained with actual label message, whereas the unconditional model is train randomly selecting labels, some of which are replaced with a null token. This approach allows di to balance diversity and consistency effectively B. Improved CNN

Inspired by earlier studies [28], this study modifies the basic CNN architecture by increasing the number of channels and incorporating a deformable convolution module [29]. The deformable convolution allows the network to adaptively adjust its receptive field, which helps capture irregular signal patterns, improving generalization to different fault types. To further refine the model, an adaptive pooling layer is employed tc reduce redundant feature interactions, effectively decreasing parameter count and enhancing the network's ability to generalize by adjusting the feature map size.

## 1) Deform Block:

This novel convolutional method involves adding offsets to standard grid sampling locations used in traditional convolution. As shown in Fig. 1(@), this crucial adjustment not only allows for free-form deformation of sampling grid, but also ensures that these deformations are dynamically learned from preceding feature maps through additional convolutional layers. This modification enhances the interaction between the convolutional filters and the 1-D input data, significantly improving the flexibility and effectiveness of feature extraction

![](./images/fupuSuT23TlfS3EAEPy836HyG8WOU2VAh.png)

Fig. 1. Illustration of deformable convolution and AdaptivePool. (a) Deform-conv. (b) AdaptivePool

## 2) Adaptive Pooling Block: This block comprises adaptiveavgpool and adaptivemaxpool, which dynamically adjust pooling windows to

meet the target output length, as shown in Eig.1(b). adaptiveavgpool smooths the input signal by averaging the values within each window, whereas the other retains the most prominent features by selecting the maximum values. This block is effectively reduces the computational complexity associated with an increasing number of channels, ensuring the model remains efficient and robust C. Quality Evaluation Method

Traditional methods of data quality evaluating, such as visual inspection and metrics like Inception Score or Frechet Inception Distance, are often designed for 2-D picture. However, these methods are not directly applicable to vibration signals, which have different characteristics and requirements. To comprehensively evaluate the quality of generated pseudosamples, this article introduces GAN-train/GAN-test [30] the feature learning ability of improved CNN and the diversity of generated samples.


## 1) GAN-Train:

This method is an evaluation metric that involves training a classifier on generated samples and then testing it on real samples. The classifier’s performance on real samples indicates the quality of generated data, in which a high GAN-train score means the generated samples are diverse and realistic enough to help classifier generalize well to real data distribution

## 2) GAN-Test:

This method is an evaluation metric that involves training a classifier on real samples and then testing it on generated samples, which is the reverse process of GAN-train. The classifier's performance on these generated samples indicates sample's realism, in which a high GAN-test score means the generated samples are realistic enough for classifier, trained on real data, to recognize them accurately. GAN-train and GAN-test are complementary evaluation metrics for assessing the performance of diffusion model. These methods ensure that generated samples can be applied to address class-imbalanced by quantifying the diversity and realism of the synthetic data

## SECTION III.

Proposed Methodology

A. Overview of the Proposed Method

The flowchart of proposed method for imbalanced intelligent fault diagnosis is shown in Fig.2. It consists of three components: data preprocessing, data augmentation, and fault classification

![](./images/fOMHPYRq8RKAEMk0EHf8yCFnS5gGqF6Fr.png)

Fig. 2. Framework of proposed data augmentation and classifier model (condition DDPM-improvec. CNN).

The overall workflow is as follows.

1) Vibration signals acquired by the sensor are sampled using a sliding window, followed by min-max normalization to maintain a consistent scale for subsequent analysis

2) Overlapping signals are employed to train condition DDPM, and backpropagation is utilized to generate synthetic samples under the guidance of label information, resulting in a mixed dataset with real samples.


 3) The mixed dataset is then used to train the improved convolutional neural network under of imbalance, with GAN-train/GAN-test methods applied to evaluate the quality of synthet B. Condition DDPM Model

As described in Fig.2, the condition DDPM introduces category labels to guide model training process and fit data distribution of multiple fault types simultaneously. Without changing the DDPM structure, the signal denoising direction is guided based on category information. Data augmentation involves two steps: training process and inverse data augmentation. During the training process, normalized vibration signals from fault samples are used to generate a sequence of samples over time t. A U-Net architecture, as part of condition DDPM, is employed to estimate the noise added during forward process, guided by label information. The loss function is used to compute gradient information of this model according to (1o), ensuring accurate estimation and correction of noise distribution

In the inverse sample augmentation process, a noisy vibration sample is drawn from standard Gaussian distribution, and then U-Net incorporates label information to guide the extracted noise sample. In the iterative process from 7 to o, the noise in the sample is estimated and iteratively removed to obtain e synthetic sample $x_0$

ResBlocks are connected in series to form the U-Net network, where each block processes input features while incorporating time and label embeddings. The process involves group normalization, Swish activation convolution, and dropout, followed by a residual connection to retain original features. This design ensures effective integration of temporal and label information, while preserving gradient flow for stable training

## C. Improved-CNN Classifier

During the fault diagnosis stage, an improved 1D-CNN with deformable convolution for time-series vibration signals is proposed to perform multiclass fault diagnosis using the generated balanced dataset. The parameter

can be seen in Fig. 2, and the architecture consists of four convolutional layers, including a deformable convolutional layer, followed by an adaptive pooling layer and a fully connected layer. Batch normalization and ReLU activations are used throughout the network. The network utilizes forward and back propagatior for training, ultimately outputting predicted fault classes. This design leverages the strengths of convolutiona and deformable convolutional layers, along with adaptive pooling, to accurately classify faults from vibratior signals.

SECTION IV.

# Fault Recognition of Rolling Bearing

Experiments were conducted on a computer with an Intel Core i3-121ooF CPU and an NVIDIA GeForce RTX 306o Ti GPU. The system had 32 GB of RAM, and the software environment included Python 3.11.9 and PyTorch 2.0.1.

## A. Experimental Data Collection

The rolling bearing dataset was obtained from a permanent magnet synchronous motor (PMSM) fault simulation test rig in laboratory, as illustrated in Fig=3. The test rig comprises six components including drive motor, load motor, message encoder, shaft coupling, vibration acquisition equipment, and electric control cabinet. The bearings under test are of type 62o4, featuring eight-ball configurations. Throughout the experiments, an acceleration sensor was placed at load side of the fault motor to acquire vibration data at a sampling frequency of 12 kHz. During the signal acquisition phase, the motor’s rotation speed and torque were manually adjusted within ranges of o3o00 r/min and o1.5 Nm, respectively. Six kinds of bearing faults and one normal condition were simulated on the platform as shown in Table I

TABLE I Details of Bearing Dataset From PMSM Platform

Fault type State
Label

Single fault
Normal condition
NC

Inner ring fault
IF

Outer ring fault
OF

Ball fault
BF

Mixed fault
Inner ring + Outer fault
IOF

Outer ring + Ball fault
OBF

Inner ring + Ball fault
IBF

![](./images/fif6D4Ef1KYCE0bX3kxbl4fwrpLH4mrPk.png)

广

Fig. 3. Test rig of PMSM health monitoring

During this experiment, the rotating speed is kept at 12oo rpm and load torque is 1 Nm; all data collection lasted 20 seconds, resulting in a total of 24oooo sampling points. Each sample contains 1024 points in time domain and is normalized using min-max normalization. Three fault scenarios, including different sampling methods, unbalanced ratios, and generation models, are simulated by manually partitioning the data into different dataset. To minimize the randomness in neural network training, a fivefold cross-validation process is utilized. In this process, the model iteration with last epoch in each fold is selected to assess the accuracy of dataset

## B. Experiment 1: Different Sample Methods

Data are sampled using different steps: 128, 256, 512, and 1024. In data splitting phase, samples from steps 128, 256, and 512 overlapped to varying degrees, contrasting with nonoverlapping samples from step 1024 Reflecting the overall distribution of sampling points, 2oo samples from overlapping categories are allocate to training data, and 1oo samples from nonoverlapping category are also designated for training purposes. For a realistic simulation of industrial application, 1oo nonoverlapping samples are specifically set aside for test data set.

In this study, a traditional CNN algorithm and four cutting-edge CNN-based algorithms for comparison with the proposed classifier model. All models are configured with the same hyperparameters: learning rate of $1\times10^{-4}$ , batch size of 64, and training duration of 5o epochs. The details and purposes of these comparison methods are listed as follows

1) CNN[28]: CNN serves as a benchmark for deep learning-based intelligent diagnosis of rotating machinery, providing valuable insights into its strengths and areas for improvement

2) MIXCNN[31]: MIXCNN enhances fault diagnosis by combining depthwise and traditional convolutions residual connections, and large kernels, achieving superior accuracy and efficiency

3) DRSN-CS[32]: DRSN-CS introduces deep residual shrinkage networks with soft thresholding, effectively removing noise and automatically improving fault diagnosis accuracy,

4) MTAGN[33]: MTAGN is a multitask fault diagnosis model designed for small sample scenarios leveraging shared features, task-specific attention, and adaptive weighting for enhanced performance

5) ResNet-HAM[34]: ResNet-HAM is a lightweight and efficient attention mechanism for ResNet integrating channel attention and spatial attention to improve feature representation

To present the results more intuitively, one of the five experimental outcomes is visualized in Eig.4, the confusion matrix is depicted in Fig.4(a), and the t-SNE visualization is shown in Fig-4(b). The experimental outcomes are presented in Table II and the losses for each mode are shown in Fig. results demonstrate that the improved CNN achieves the highest average accuracy across fivefol experiments, reaching 99.66%, thereby consistently exhibiting superior diagnostic capabilities c the other methods. It shows improvements of 2.74%, 2.17%, 2.o0%, and $8.51\%$ over the baseline CNN Furthermore, the improved CNN exhibits superior stability relative to the other algorithms, as indicated by its smallest standard deviation among the methods compared. In terms of training time and parameters, the proposed model has the highest parameter count but is not the most time-consuming, indicating a balanced trade-off between performance and computational efficiency

TABLE II Performance of Different CNN Models: Comparison of Accuracy, Parameters, and Training Time

| Accuracy (%) | 128 | 256 | 512 | 1024 |
|--------------|-----|-----|-----|------|
| CNN          | 96.80 ±0.79 | 97.34 ±1.10 | 97.66 ±0.64 | 90.69 ±2.18 | 0.18M | 7.17S |
| MIXCNN       | 96.77 ±2.09 | 98.20 ±0.66 | 99.11 ±0.40 | 82.57 ±3.17 | 0.08M | 21.96S |
| DRSCN        | 96.20 ±0.67 | 98.31 ±0.83 | 99.34 ±0.30 | 98.69 ±0.56 | 5.25M | 84.09S |
| MTAGN        | 98.51 ±0.46 | 99.51 ±0.70 | 99.14 ±0.29 | 92.77 ±3.89 | 0.16M | 14.6S |
| ResNet-HAM   | 99.11 ±0.54 | 99.23 ±0.16 | 99.51 ±0.13 | 99.11 ±0.19 | 3.85M | 16.37S |
| Proposed     | 99.54 ±0.12 | 99.61 ±0.16 | 99.66 ±0.16 | 99.20 ±0.16 | 9.35M | 27.91S |

TABLE III Performance Metrics of Different Unbalanced Ratio in PMSM

Accuracy
Unbalanced dataset: 
A. CNN-1 77.09 ± 2.64, 77.79 ± 2.60, 78.53 ± 2.60, 79.00 ± 2.60
B. CNN-2 77.09 ± 2.64, 77.79 ± 2.60, 78.53 ± 2.60, 79.00 ± 2.60
C. MNINCN 77.09 ± 2.64, 77.79 ± 2.60, 78.53 ± 2.60, 79.00 ± 2.60
D. DRN-CNN-3 77.09 ± 2.64, 77.79 ± 2.60, 78.53 ± 2.60, 79.00 ± 2.60

With condition (DIPLM): 
A. CNN-1 77.09 ± 2.64, 77.79 ± 2.60, 78.53 ± 2.60, 79.00 ± 2.60
B. CNN-2 77.09 ± 2.64, 77.79 ± 2.60, 78.53 ± 2.60, 79.00 ± 2.60
C. MNINCN 77.09 ± 2.64, 77.79 ± 2.60, 78.53 ± 2.60, 79.00 ± 2.60
D. DRN-CNN-3 77.09 ± 2.64, 77.79 ± 2.60, 78.53 ± 2.60, 79.00 ± 2.60

Proposed: 97.34 ± 0.82, 98.55 ± 0.82, 97.34 ± 0.82, 98.55 ± 0.82

![](./images/fofgmG0K6UXFQyYWMhlNpI16R397rBRL7.png)

## Fig. 4. Visualization of one of the five experimental improved CNN results. (a) Confusion matrix. (b) t-

SNE Visualization of Features.

![](./images/funqsvt4Tbu8hZd1iiBfeRqQGSmyMpNRK.png)

Fig. 5. Average loss curve of the training process

As the step size increases, all models show improvements in accuracy to varying degrees. However, when the step size reaches 1024,there is a noticeable tendence of decreasing accuracy. This decline is primarily due to the smaller number of samples obtained using the nonoverlapping method within a fixed total sample length. Overlapping methods, which use smaller step sizes, generate more training samples compared to nonoverlapping method. Constructing samples of length 1o24 with an overlapping step reduces the total sample length whereas maintaining a high accuracy rate.

To further evaluate the robustness of the improved CNN against noise, Gaussian noise with varying signalto-noise ratios (SNRs) was added to raw vibration. The specific expression i

$$\mathrm{SNR(dB)}=10\log_{10}\biggl(\frac{P_{\mathrm{signal}}}{P_{\mathrm{noise}}}\biggr)$$

## View Source

where $P_{\text{signal}}$ stands for the useful signal power, $P_{\text{noise}}$ stands for the noise power, and dB is a logarithmic unit used to express the ratio of the two. The performance of the improved CNN was compared to baseline methods, with each method being tested at SNR levels of -4, -2, 0, 2, 4, and 6 dB for the vibration signals. The results are displayed in Fig. 6. As the noise level increases, the diagnostic accuracy of all models declines However, the improved CNN proposed in this article demonstrates significant advantages in noise resistance. At a simulated noise level of $-4dB$ , the accuracy of the proposed classification model reaches

83.66%, which is significantly higher than that of other methods. Additionally, the improved CNN demonstrates superior performance under moderate and low noise conditions. These findings indicate that the improved CNN effectively addresses the issue of mechanical fault classification and is highly capable of handling diagnostic signals in noisy environments.

![](./images/fUF5GokskW1Bfv2x9XnqtpvHtb58sePUb.png)

F

Fig.6. Diagnostic accuracy of different methods using PMSM bearing dataset under different SNR conditions

## C.Experiment 2:Different Unbalanced Ratios

To validate the data generation ability of the condition DDPM and the fault diagnosis ability of improve CNN in small sample and unbalanced dataset, three unbalanced datasets (B, C, and D) with ratios of 1:2, 1:4 and 1:1o are generated based on PMSM bearing dataset. The number of sample for specific fault classes is presented in Table IV, and Dataset A is employed as the training set for generative model, where
is utilized as the test set for classification algorithm

TABLE IV Details of Bearing Dataset From PMSM Platform

Dataset Normal Fault Sample step Unbalanced ratio
A 200 200 128 \
B 200 100 128 2
C 200 50 128 4
D 200 20 128 10
E 100 100 1024 \

In this experiment, two experimental approaches are utilized to address the imbalance in this bearing dataset The first approach involves selecting a number of normal samples that is equal to the number of faulty samples to achieve balance; The second approach supplements the dataset with synthetic samples generated through condition DDPM. Each rebalanced dataset serves as training set for the classifier model described in experiment 1, utilizing consistent hyperparameters across all models.

The results are shown in Table III. This indicate that with the number of training samples decreases, the accuracy of all methods also declines. Nevertheless, it is evident that the proposed generation and classification model consistently outperforms other approaches in all scenarios, achieving significantly higher average accuracy. When class-imbalanced ratio of original data is 1:2 and 1:4, improved CNN obtain 97.34% and 95-51% diagnostic accuracy, which is the highest accuracy for all classification models. When unbalanced

ratio reached 1:1o, all models exhibits a significant decrease in accuracy due to lack of sufficient learnable features.Despite this challenge, the proposed method maintains an accuracy of 81.31%, demonstrating it ability to capture characteristic features of minority classes. For small samples, the diagnosis accuracy exhibits variability compared to the unbalanced dataset, primarily due to the interference of normal signals with fault signal identification. Despite this, the improved CNN consistently achieves the highest accuracy among the models.

Furthermore, synthesizing data through condition DDPM has significantly improved the performance of classifier model across all imbalanced datasets. Notably, with this data augmentation method, improved CNN maintains an accuracy level exceeding 98%. This improvement is particularly pronounced in the scenario with a ratio of 1:1o, where the accuracy of the proposed model escalated from an initial 81.31%98.o9%, achieving an increase of 16.78%. The accuracy improvements in other classifier models suggest that condition DDPM can be applied to enhance the learning of characteristics in conditions with imbalanced samples D. Experiment 3: Different Generation Models

To evaluate the quality of data generated by the condition DDPM, five generative models and two oversampling methods are constructed for comparison. Additionally, GAN-train/GAN-test is introduced to separately illustrate the utilization potential of the synthetic data. The generative models include CGAN, WGAN, WGAN-GP, ACGAN, and DDPM, whereas the oversampling methods include ADASYN and SMOTEENN. All these models are applied to rebalance dataset D and tested on dataset E. The generative methods are trained on dataset A, and the oversampling methods are specifically based on dataset D. The specific training hyperparameter settings are shown in Table V

TABLE V Training Hyperparameter Settings of Generative Models

Model Optimizer Learning Rate Batch size Epoch Loss
CGAN Adam 0.00001 64 1000 BCE
WGAN Adam 0.00001 64 1000 MSE
WGAN-GP Adam 0.00001 64 1000 WD
ACGAN Adam 0.0002 64 1000 hinge_v2
DDPM Adam 0.00001 64 300 MAE
Proposed Adam 0.00001 64 300 MAE

In this experiment, different generative methods are constructed to illustrate the signal generation capabilit of condition DDPM. The results are shown in Table VI. It shows that the diagnostic accuracy of proposec model has increased with data augmentation. With some classification models, traditional oversampling methods even outperform deep learning methods like CGAN, WGAN-GP, and ACGAN. The GAN-based generation models are easily affected by noise, which can result in synthetic data with blurred fe cannot be classified effectively. Additionally, DDPM is not universally effective across all classifi without involving label information, as this can lead to confusion between generated samples with differen fault types. However, condition DDPM performs much better than other methods, achieving an accuracy of above 90% in all classifier model and reaching up to $98.17\%$ in the proposed classifier model, even with an unbalanced ratio of 1:1o. It means that the data generated by proposed model is of greater variety and quality due to labeling guidance in data generation process.

TABLE VI Fault Classification of Different Data Auqmentation on Bearinq Dataset D

F

The total training times of the generative models are shown in Table VII. It can be observed that the training time of the proposed conditional DDPM is 1:o7:86, which outperforms the original DDPM and is longer than GAN-based methods. With the incorporation of gradient penalty computation, the training process for WGAN-GP demands more time than that of other GAN architectures. The main reason for the extended training time of the original DDPM is that training on individual fault samples results in prolonged overall training duration. As a result, the conditional DDPM with label information faeilitates the model training procedure and achieves good performance in terms of computational efficiency and the quality of synthetic samples

TABLE VII Total Training Time of Generative Models

| | Time Hours |
|--------|-----------|
| CGAN | 0:11.78 |
| WGAN | 1:12.43 |
| WGAN-GP| 0:16.36 |
| ACGAN | 0:11.22 |
| DDPM | 4:23.40 |
| Proposed| 1:07.86 |

D

The performance of proposed condition DDPM under diffeent number of generated sample is shown in Fig. 7. Using GAN-train/GAN-test to indicate the similarity and diversity of synthetic data, it is observed the number of synthetic sample increases, diagnosis accuracy of improved CNN also increases. T and testing accuracies are relatively close to each other, indicating that generated data are similar signal and contains valid information that expresses the characteristics of fault class. When the generatec sample count reaches 2oo, both GAN-train and GAN-test achieve a relatively stable state. As the number of sample continues to increase, the fluctuations in GAN-test expand slightly, whereas GAN-train continues to rise. This indicates that the diversity of generated sample improves and model trained on real samples does not fully recognize the generated samples, thus achieving a state of a mixture of real and generated samples. By incorporating synthetic data into training process, the classification model can more effectively explore distinguishing features between various faults, leading to improved fault classification

![](./images/fD4KLFNXr5fOG5eHtwlq6D2BOGS7lnpf6.png)

Figm are the faut diagnosis accuracy under iferent number of PMSM generated samples

## E. Experiment 4: Performance Evaluation on CWRU Dataset

The Case Western Reserve University (CWRU) dataset is a widely applied experimental bearing dataset [35] The test rig includes a two-horsepower motor, a torque sensor, a power meter, and an electronic controller Single-point defects are induced in the bearings using electrical discharge machining, with fault diameters of 7, 14, 21, and 28 mils. Data collection occurs at a sampling frequency of 12 kHz under four load conditions: 01, 2, and 3 horsepower. The dataset encompasses bearings in both normal and various faulty states, categorized into inner race fault (IF), outer race fault (OF), and ball fault (BF).

In this experiment, three fault types with extent of 7, 14, 21 mils, and normal condition, are used to evaluate the generation performance of the proposed method. The selected data length is 1o24, with a sliding window length of 256. The dataset setup is similar with experiment 2 $(B^*,C^*$,and $D^*$ D* $D^{*}$ ) with unbalanced ratio of 1:2, 1:4, and 1:10 as shown in Table IV

levels used in experiment 1 were applied to the CWRU dataset. The results are shown in Fig.8. As the noise

To further evaluate the robustness of the improved CNN against noise on another dataset, the sa level increases, the performance of all other models decreases significantly, whereas the proposed classifier model maintains the highest accuracy and is the least affeted by the noise. Considering comparison across different datasets, the same deep learning methods used in experiment 2 and experiment 3 are chosen for training and compared. The experimental results are shown in Tables VIII and IX and Fig=9. The average accuracy of condition DDPM on the rebalanced dataset significantly increases due to the sufficient synthetic data, rising from 89.30% to 98.96%. This demonstrates that the generated data are of high quality and diversity. Additionally, the proposed classifier model obtains the highest accuracy among other classifier models in CWRU dataset, further illustrating its universality and effectiveness

TABLE VIII Performance Metrics of Different Unbalanced Ratio in CWRU

Accuracy
% (%)

中

![](./images/fgnGbiBo1CpnLfudkfUbWkWbatPMd39hS.png)

E

Fig.8. Diagnostic accuracy of different methods using CWRU dataset under different SNR conditions.

![](./images/f9fubGXRDzNPKpGeqvY216lHAXSpKIvdk.png)
Fig.9.

 Compare the fault diagnosis accuracy under different number of CWRU generated samples.

Compared to other data augmentation methods, the condition DDPM achieves the best accuracy in diagnosis across different classifier models, as shown in Table IX, demonstrating its universality and applicability to various classifier models. Additionally, data augmentation with GAN-based models consistently outperforms traditional oversampling methods in most classifier models. However, the performance of individual GAN derivative models shows slight variations, primarily due to the lack of hyperparameter optimization, which can lead to blurred features often perceived as noisy data. Besides, in contrast to standard DDPM, which operates without label information, condition DDPM demonstrates significantly enhanced performance. This

improvement is attributed to incorporation of category-specific information for different fault types during generation process. By leveraging this detailed category information, condition DDPM achieves superiol generation quality, particularly in capturing and emphasizing class-distinct features

TABLE IX Fault Classification of Different Generative Methods on CWRU Bearing Dataset D*

| | Accuracy (%) |
| --- | --- |
| | CNN | MIXCNN | DRSCN-CS | MTAGN | ResNet-HAM | Proposed |
| ADASYN | $90.10 \pm 2.02$ | $96.10 \pm 1.25$ | $94.98 \pm 0.51$ | $95.12 \pm 0.81$ | $93.04 \pm 1.37$ | $94.26$ |
| SMOTEENN | $90.68 \pm 0.53$ | $97.20 \pm 0.65$ | $95.38 \pm 0.31$ | $94.46 \pm 2.01$ | $95.54 \pm 0.82$ | $95.21$ |
| CGAN | $90.30 \pm 1.24$ | $94.40 \pm 1.61$ | $97.78 \pm 0.58$ | $96.10 \pm 0.48$ | $96.88 \pm 0.89$ | $96.92$ |
| WGAN | $90.40 \pm 1.49$ | $91.96 \pm 1.76$ | $98.32 \pm 0.23$ | $97.60 \pm 0.70$ | $98.56 \pm 0.23$ | $98.16$ |
| WGAN-GP | $92.98 \pm 1.49$ | $92.06 \pm 1.01$ | $98.14 \pm 0.19$ | $96.02 \pm 1.86$ | $97.88 \pm 0.39$ | $97.92$ |
| ACGAN | $86.30 \pm 2.28$ | $94.76 \pm 2.09$ | $97.80 \pm 0.82$ | $95.16 \pm 1.87$ | $97.58 \pm 1.40$ | $95.92$ |
| DDPM | $88.98 \pm 2.09$ | $81.84 \pm 2.44$ | $96.84 \pm 0.39$ | $92.57 \pm 2.57$ | $97.10 \pm 0.34$ | $96.18$ |
| Proposed | $96.62 \pm 0.54$ | $97.06 \pm 0.79$ | $98.76 \pm 0.09$ | $96.62 \pm 1.22$ | $98.62 \pm 0.80$ | $98.92$ |

As illustrated in Fig_ 9, with an increase in the number of generated sample, GAN-train accuracy trend smooths and stabilizes around $98\%$ , while GAN-test accuracy reaches above 99%. Although there is a slight discrepancy between training and testing data, which indicates that synthetic data are more similar to real samples but has less diversity, this high accuracy achieved by proposed data generation and classifier model also illustrates that generated samples are equivalent to real samples and can be effectively used to address data imbalance problems

## SECTION V. Ablation Studies

### A. Effectiveness of the Improvements

Ablation experiments were conducted on Datasets A as shown in Table IV, to evaluate the effecti different modules in the improved CNN, which include more channels, adaptive pooling, and deformable convolution. The results, shown in Table X, indicate that Model 4, incorporating all three modules, achieves the best diagnostic performance. Model 1 lacks channel expansion, resulting in a 1.94% decrease in performance. Model 2 omits adaptive pooling, leading to a $0.65\%$ reduction in accuracy and an increase in parameter count. Model 3 excludes deformable convolution, which slightly reduces accuracy by o.23%, with a minimal impact on the number of parameters. These findings suggest that incorporating these module effectively enhances the model's diagnostic capabilities, achieving an optimal balance between accuracy and efficiency.

TABLE X Results of the Ablation Experiment

Model Number More Channel Adaptive Deform Pooling Conv Parameters (M) Training time (S) Accuracy (%) 1 × √ × √ 0.56 8.7 97.60 ± 1.02 2 × √ × √ 64.5 68.38 98.89 ± 0.40 3 × √ √ √ 8.93 22.75 99.31 ± 0.42 4 × √ √ √ 9.35 27.91 99.54 ± 0.12

## B. Visualization of Synthetic Samples

To directly visualize the quality of synthetic samples from the PMSM and CWRU dataset, a comparison between the time domain and frequency spectra (obtained using Fast Fourier Transform) of both synthetic

and real samples under failure conditions is illustrated in Figs. 10 and 11. It can be observed that the synthetic samples generated by conditional DDPM not only produce visually plausible outputs but also effectively retain the key characteristic frequencies of the real samples in the frequency domain and accurately preserve the pulse fluctuations associated with faults in the time domain. This indicates that the generative model successfully captures the underlying statistical properties of real data and facilitates the generation of fault specific samples, making it especially valuable in fault diagnosis applications where real fault data are limited

![](./images/fqwylUQDIh8tXn55Dkgohig5SIORlbg6F.png)
Fig.10.

Time and frequency domain analysis of PMSM Dataset.

![](./images/f1AYcOWRZIwE0GS4TyvgaIBmAoTq8CZQl.png)
Fig.11.

Time and frequency domain analysis of CWRU Dataset.

## SECTION VI. Conclusion

In this article, a novel data augmentation fault diagnosis method based on condition DDPM and improvec CNN is proposed. 1-D time-series vibration is utilized to train diffusion model, and this data generation involves sampling from Gaussian noise and applying a series of reverse denoising process with the guidance of label information. Subsequently, an improved CNN with deformable convolution is employed to extend the perceptual range and enhance classifier model's ability to learn fault characteristics. Finally, a comprehensive methodology for evaluating generated data is introduced, providing a means to assess both similarity and diversity. This methodology detailed here encompasses the complete process of data augmentation and fault classification. This approach has been experimentally validated using PMSM and CWRU bearing dataset comparing with five classifier models alongside seven data augmentation techniques. Experimental results indicate that the proposed model can generate high-quality samples and attains high fault classification accuracy, even dealing with class-imbalanced datasets. These findings suggest the model’s potential for application across various mechanical systems, as well as its adaptability to other types of time-series signals such as sound and electrical current signals

Since the dataset used in this study is collected in a fixed operational environment, its applicability is limited to similar work conditions. Additionally, the U-Net architecture used in this study is complex and requires substantial computational resources. Therefore, future work includes applying transfer learning to this mode for different scenarios, generating synthetic data under various operational conditions, and exploring different architectures of U-Net to reduce compute consumption
