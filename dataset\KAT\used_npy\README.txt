KAT数据集 - NPY格式数据说明
==============================

数据格式说明:
- NPY文件格式: 
  * 训练数据: kat_data.npy (样本矩阵), kat_label.npy (整数类型标签)
  * 测试数据: kat_data.npy (样本矩阵), kat_label.npy (整数类型标签)

标签说明:
- 0: K001
- 1: KA01
- 2: KA05
- 3: KA09
- 4: KI01
- 5: KI03
- 6: KI05
- 7: KI08

样本数量:
- 训练集: 每个类别约 200 个样本 (总计约 1600 个样本)
- 测试集: 每个类别约 200 个样本 (总计约 1600 个样本)

采样方法:
- 从每个类别的每个文件中随机提取样本，允许样本重叠
- 训练样本从信号的前70%长度选取，测试样本从后30%长度选取
- 每个样本包含 1024 个数据点
