% MAFAULDA数据集预处理程序
% 处理MAFAULDA轴承故障数据集，数据分割并保存为MATLAB和NPY格式
% 数据集包含15个类别，每个类别包含6列数据

% 清空工作区和命令窗口
clear;
clc;

try
    % 开始计时
    tic;
    
    % 调用处理函数
    process_MAFAULDA_dataset();
    
    % 结束计时
    elapsed_time = toc;
    fprintf('处理完成! 耗时: %.2f 秒\n', elapsed_time);
catch e
    % 捕获并显示错误
    fprintf('处理过程中发生错误:\n');
    disp(e.message);
    if ~isempty(e.stack)
        fprintf('错误位置: %s (行 %d)\n', e.stack(1).name, e.stack(1).line);
    end
end

% ================== 主处理函数 ==================
function process_MAFAULDA_dataset()
    % ==================== 参数设置 ====================
    sample_length = 1024;           % 每个样本的长度（采样点数）
    train_samples_per_class = 200;  % 每个类别生成的训练样本数量
    test_samples_per_class = 200;    % 每个类别生成的测试样本数量
    data_column = 1;                % 使用数据矩阵的第几列（1-6）
    random_seed = 42;               % 随机种子，确保结果可重现
    
    % ==================== 路径设置 ====================
    % 获取脚本所在目录的上级目录作为基础路径
    script_dir = fileparts(mfilename('fullpath'));
    base_dir = fileparts(script_dir);  % 上级目录
    
    % 使用基础路径构建完整路径
    ori_path = fullfile(base_dir, 'dataset', 'MAFAULDA', 'ori_mat');      % 原始数据目录
    final_mat_path = fullfile(base_dir, 'dataset', 'MAFAULDA', 'used_mat');  % 最终mat文件保存目录
    final_npy_path = fullfile(base_dir, 'dataset', 'MAFAULDA', 'used_npy');  % 最终npy文件保存目录
    
    fprintf('使用以下路径:\n');
    fprintf('原始数据目录: %s\n', ori_path);
    fprintf('Mat文件保存目录: %s\n', final_mat_path);
    fprintf('NPY文件保存目录: %s\n', final_npy_path);
    fprintf('使用数据矩阵的第 %d 列\n', data_column);
    
    % 检查原始数据目录是否存在
    if ~exist(ori_path, 'dir')
        error('原始数据目录不存在: %s', ori_path);
    end
    
    fprintf('开始处理MAFAULDA数据集...\n');
    
    % ==================== 创建保存目录 ====================
    if ~exist(fullfile(final_mat_path, 'train'), 'dir')
        mkdir(fullfile(final_mat_path, 'train'));
    end
    if ~exist(fullfile(final_mat_path, 'test'), 'dir')
        mkdir(fullfile(final_mat_path, 'test'));
    end
    if ~exist(fullfile(final_npy_path, 'train'), 'dir')
        mkdir(fullfile(final_npy_path, 'train'));
    end
    if ~exist(fullfile(final_npy_path, 'test'), 'dir')
        mkdir(fullfile(final_npy_path, 'test'));
    end
    
    % ==================== 读取原始数据 ====================
    fprintf('读取原始数据文件...\n');
    
    % 获取所有子目录（类别目录）
    main_dirs = dir(ori_path);
    main_dirs = main_dirs([main_dirs.isdir]);
    main_dirs = main_dirs(~ismember({main_dirs.name}, {'.', '..'}));
    
    % 用于存储所有类别的原始信号和标签
    class_signals = cell(15, 1);  % 存储最多15个类别的原始信号
    class_names = cell(15, 1);    % 存储类别名称
    
    % 类别计数
    class_count = 0;
    
    % 遍历所有主目录
    for i = 1:length(main_dirs)
        main_dir_path = fullfile(ori_path, main_dirs(i).name);
        
        % 获取子目录或直接的mat文件
        sub_items = dir(main_dir_path);
        
        % 首先查找当前目录下的mat文件
        mat_files = sub_items(endsWith({sub_items.name}, '.mat') & ~[sub_items.isdir]);
        
        if ~isempty(mat_files)
            % 当前目录下有mat文件，直接处理
            for j = 1:length(mat_files)
                class_count = class_count + 1;
                if class_count > 15
                    warning('超过15个类别，忽略剩余类别');
                    break;
                end
                
                file_path = fullfile(main_dir_path, mat_files(j).name);
                [~, file_name, ~] = fileparts(mat_files(j).name);
                class_names{class_count} = file_name;
                
                fprintf('处理类别 %d: %s\n', class_count-1, class_names{class_count});
                
                try
                    % 加载MAT文件
                    data = load(file_path);
                    field_names = fieldnames(data);
                    
                    if length(field_names) ~= 1
                        warning('文件 %s 包含多个变量，使用第一个变量', file_path);
                    end
                    
                    % 获取数据矩阵
                    data_matrix = data.(field_names{1});
                    
                    % 检查数据矩阵的维度
                    if size(data_matrix, 2) < data_column
                        warning('文件 %s 的数据列数不足，跳过处理', file_path);
                        class_signals{class_count} = [];
                        continue;
                    end
                    
                    % 提取指定列的数据
                    class_signals{class_count} = data_matrix(:, data_column);
                    
                    fprintf('类别 %d: 提取了 %d 个数据点\n', class_count-1, length(class_signals{class_count}));
                catch e
                    warning('处理文件 %s 时出错: %s', file_path, e.message);
                    class_signals{class_count} = [];
                end
            end
        else
            % 检查子目录
            sub_dirs = sub_items([sub_items.isdir]);
            sub_dirs = sub_dirs(~ismember({sub_dirs.name}, {'.', '..'}));
            
            for j = 1:length(sub_dirs)
                sub_dir_path = fullfile(main_dir_path, sub_dirs(j).name);
                
                % 获取子目录中的mat文件
                sub_mat_files = dir(fullfile(sub_dir_path, '*.mat'));
                
                for k = 1:length(sub_mat_files)
                    class_count = class_count + 1;
                    if class_count > 15
                        warning('超过15个类别，忽略剩余类别');
                        break;
                    end
                    
                    file_path = fullfile(sub_dir_path, sub_mat_files(k).name);
                    class_name = sprintf('%s_%s', sub_dirs(j).name, sub_mat_files(k).name(1:end-4));
                    class_names{class_count} = class_name;
                    
                    fprintf('处理类别 %d: %s\n', class_count-1, class_names{class_count});
                    
                    try
                        % 加载MAT文件
                        data = load(file_path);
                        field_names = fieldnames(data);
                        
                        if length(field_names) ~= 1
                            warning('文件 %s 包含多个变量，使用第一个变量', file_path);
                        end
                        
                        % 获取数据矩阵
                        data_matrix = data.(field_names{1});
                        
                        % 检查数据矩阵的维度
                        if size(data_matrix, 2) < data_column
                            warning('文件 %s 的数据列数不足，跳过处理', file_path);
                            class_signals{class_count} = [];
                            continue;
                        end
                        
                        % 提取指定列的数据
                        class_signals{class_count} = data_matrix(:, data_column);
                        
                        fprintf('类别 %d: 提取了 %d 个数据点\n', class_count-1, length(class_signals{class_count}));
                    catch e
                        warning('处理文件 %s 时出错: %s', file_path, e.message);
                        class_signals{class_count} = [];
                    end
                    
                    if class_count >= 15
                        break;
                    end
                end
                
                if class_count >= 15
                    break;
                end
            end
        end
        
        if class_count >= 15
            break;
        end
    end
    
    % 调整class_signals和class_names大小，以实际类别数为准
    class_signals = class_signals(1:class_count);
    class_names = class_names(1:class_count);
    
    % 检查是否成功提取了样本
    if all(cellfun(@isempty, class_signals))
        error('未能成功提取任何样本。请检查数据文件和处理逻辑。');
    end
    
    % ==================== 从信号中随机选择样本 ====================
    fprintf('从信号中随机选择样本...\n');
    
    % 设置随机种子以确保结果可重现
    rng(random_seed);
    
    % 准备存储训练和测试样本
    all_train_data = [];
    all_train_labels = [];
    all_test_data = [];
    all_test_labels = [];
    
    % 按类别处理
    for class_id = 0:(class_count-1)
        fprintf('处理类别 %d 的样本...\n', class_id);
        
        % 获取当前类别的信号
        signal = class_signals{class_id+1};
        
        if isempty(signal)
            warning('类别 %d 的信号为空，跳过处理', class_id);
            continue;
        end
        
        % 确保信号长度足够
        if length(signal) < sample_length
            warning('类别 %d 的信号长度不足 (%d < %d)，跳过处理', ...
                class_id, length(signal), sample_length);
            continue;
        end
        
        % 从信号中随机提取训练样本
        train_samples = extract_random_samples(signal, sample_length, train_samples_per_class);
        train_labels = ones(size(train_samples, 1), 1) * class_id;
        
        % 从信号中随机提取测试样本
        test_samples = extract_random_samples(signal, sample_length, test_samples_per_class);
        test_labels = ones(size(test_samples, 1), 1) * class_id;
        
        % 添加到总样本集
        all_train_data = [all_train_data; train_samples];
        all_train_labels = [all_train_labels; train_labels];
        all_test_data = [all_test_data; test_samples];
        all_test_labels = [all_test_labels; test_labels];
        
        fprintf('类别 %d: 生成了 %d 个训练样本和 %d 个测试样本\n', ...
            class_id, size(train_samples, 1), size(test_samples, 1));
    end
    
    % 随机打乱训练集和测试集
    train_shuffle_idx = randperm(size(all_train_data, 1));
    all_train_data = all_train_data(train_shuffle_idx, :);
    all_train_labels = int32(all_train_labels(train_shuffle_idx));  % 转换为int32类型
    
    test_shuffle_idx = randperm(size(all_test_data, 1));
    all_test_data = all_test_data(test_shuffle_idx, :);
    all_test_labels = int32(all_test_labels(test_shuffle_idx));  % 转换为int32类型
    
    fprintf('最终训练集: %d 个样本\n', size(all_train_data, 1));
    fprintf('最终测试集: %d 个样本\n', size(all_test_data, 1));
    
    % ==================== 可视化样本 ====================
    fprintf('可视化样本...\n');
    
    % 计算需要绘制的子图行数和列数
    num_classes = length(unique(all_train_labels));
    num_rows = ceil(sqrt(num_classes));
    num_cols = ceil(num_classes/num_rows);
    
    figure('Name', 'MAFAULDA数据集样本示例', 'Position', [100, 100, 1200, 800]);
    
    % 为每个类别可视化一个样本
    for class_id = 0:(num_classes-1)
        class_idx = find(all_train_labels == class_id, 1);
        if ~isempty(class_idx)
            subplot(num_rows, num_cols, class_id+1);
            plot(all_train_data(class_idx, :));
            title(sprintf('类别 %d: %s', class_id, class_names{class_id+1}));
        end
    end
    
    % 保存样本可视化图像
    saveas(gcf, fullfile(final_mat_path, 'samples_visualization.png'));
    
    % ==================== 保存为MATLAB格式 ====================
    fprintf('保存为MATLAB格式...\n');
    
    % 保存训练集
    save(fullfile(final_mat_path, 'train', 'train_dataset.mat'), 'all_train_data', 'all_train_labels');
    
    % 保存测试集
    save(fullfile(final_mat_path, 'test', 'test_dataset.mat'), 'all_test_data', 'all_test_labels');
    
    % ==================== 生成并执行Python脚本来转换为NPY格式 ====================
    fprintf('生成并执行Python脚本来转换为NPY格式...\n');
    
    % 创建临时Python脚本文件
    temp_dir = fullfile(tempdir, 'mafaulda_temp');
    if ~exist(temp_dir, 'dir')
        mkdir(temp_dir);
    end
    
    py_script_path = fullfile(temp_dir, 'convert_to_numpy.py');
    fid = fopen(py_script_path, 'w');
    
    if fid < 0
        error('无法创建Python转换脚本: %s', py_script_path);
    end
    
    % 写入Python脚本内容
    fprintf(fid, '#!/usr/bin/env python\n');
    fprintf(fid, '# -*- coding: utf-8 -*-\n\n');
    fprintf(fid, '# 此脚本由MATLAB自动生成，用于将MAFAULDA数据集从MAT格式转换为NPY格式\n\n');
    
    fprintf(fid, 'import numpy as np\n');
    fprintf(fid, 'import scipy.io as sio\n');
    fprintf(fid, 'import os\n\n');
    
    fprintf(fid, '# 创建输出目录\n');
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n', strrep(fullfile(final_npy_path, 'train'), '\', '\\'));
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n\n', strrep(fullfile(final_npy_path, 'test'), '\', '\\'));
    
    fprintf(fid, '# 加载训练数据\n');
    fprintf(fid, 'print("加载训练数据...")\n');
    fprintf(fid, 'train_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'train', 'train_dataset.mat'), '\', '\\'));
    
    fprintf(fid, '# 加载测试数据\n');
    fprintf(fid, 'print("加载测试数据...")\n');
    fprintf(fid, 'test_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'test', 'test_dataset.mat'), '\', '\\'));
    
    fprintf(fid, '# 保存训练数据\n');
    fprintf(fid, 'print("保存训练数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", train_data["all_train_data"])\n', strrep(fullfile(final_npy_path, 'train', 'mafaulda_data.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", train_data["all_train_labels"])\n\n', strrep(fullfile(final_npy_path, 'train', 'mafaulda_label.npy'), '\', '\\'));
    
    fprintf(fid, '# 保存测试数据\n');
    fprintf(fid, 'print("保存测试数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", test_data["all_test_data"])\n', strrep(fullfile(final_npy_path, 'test', 'mafaulda_data.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", test_data["all_test_labels"])\n\n', strrep(fullfile(final_npy_path, 'test', 'mafaulda_label.npy'), '\', '\\'));
    
    fprintf(fid, 'print("转换完成！")\n');
    fclose(fid);
    
    % 创建说明文件
    readme_path = fullfile(final_npy_path, 'README.txt');
    fid = fopen(readme_path, 'w');
    if fid > 0
        fprintf(fid, 'MAFAULDA数据集 - NPY格式数据说明\n');
        fprintf(fid, '==============================\n\n');
        fprintf(fid, '数据格式说明:\n');
        fprintf(fid, '- NPY文件格式: \n');
        fprintf(fid, '  * 训练数据: mafaulda_data.npy (样本矩阵), mafaulda_label.npy (整数类型标签)\n');
        fprintf(fid, '  * 测试数据: mafaulda_data.npy (样本矩阵), mafaulda_label.npy (整数类型标签)\n\n');
        fprintf(fid, '标签说明:\n');
        for i = 1:length(class_names)
            fprintf(fid, '- %d: %s\n', i-1, class_names{i});
        end
        fprintf(fid, '\n');
        fprintf(fid, '样本数量:\n');
        fprintf(fid, '- 训练集: 每个类别约 %d 个样本 (总计约 %d 个样本)\n', train_samples_per_class, train_samples_per_class*length(class_names));
        fprintf(fid, '- 测试集: 每个类别约 %d 个样本 (总计约 %d 个样本)\n\n', test_samples_per_class, test_samples_per_class*length(class_names));
        fprintf(fid, '采样方法:\n');
        fprintf(fid, '- 从每个类别的每个文件中随机提取样本，允许样本重叠\n');
        fprintf(fid, '- 每个样本包含 %d 个数据点\n', sample_length);
        fclose(fid);
    end
    
    % 执行Python脚本
    fprintf('正在执行Python脚本以生成NPY格式数据文件...\n');
    
    try
        % 判断系统平台
        if ispc
            % Windows系统
            [status, cmdout] = system(['python "', py_script_path, '"']);
        else
            % Linux/Mac系统
            [status, cmdout] = system(['python3 "', py_script_path, '"']);
        end
        
        if status == 0
            fprintf('Python脚本执行成功！\n%s\n', cmdout);
        else
            fprintf('Python脚本执行失败！错误码: %d\n%s\n', status, cmdout);
            fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
            fprintf('python "%s"\n', py_script_path);
        end
    catch e
        fprintf('执行Python脚本时出错: %s\n', e.message);
        fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
        fprintf('python "%s"\n', py_script_path);
    end
    
    % 尝试删除临时Python脚本
    try
        delete(py_script_path);
        rmdir(temp_dir);
        fprintf('临时Python脚本已删除\n');
    catch
        fprintf('无法删除临时Python脚本: %s\n', py_script_path);
    end
    
    fprintf('MAFAULDA数据集预处理完成!\n');
end

function samples = extract_random_samples(signal, sample_length, num_samples)
    % 从信号中随机提取固定长度的样本
    % 输入:
    %   signal: 原始信号
    %   sample_length: 每个样本的长度
    %   num_samples: 需要提取的样本数量
    % 输出:
    %   samples: 提取的样本矩阵，每行一个样本
    
    signal_length = length(signal);
    max_start_idx = signal_length - sample_length + 1;
    
    % 检查信号长度是否足够
    if max_start_idx <= 0
        error('信号长度不足以提取长度为%d的样本', sample_length);
    end
    
    % 初始化样本矩阵
    samples = zeros(num_samples, sample_length);
    
    % 随机选择起始位置并提取样本
    for i = 1:num_samples
        start_idx = randi(max_start_idx);
        end_idx = start_idx + sample_length - 1;
        samples(i, :) = signal(start_idx:end_idx);
    end
end 