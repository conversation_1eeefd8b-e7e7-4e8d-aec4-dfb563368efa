"""
数据增强方法工厂模块
统一管理和创建各种数据增强方法
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, Tuple, Optional, Any
import logging
import os
from tqdm import tqdm
from .augmentation_methods import (
    CGAN, WGAN, WGAN_GP, DCGAN, DDPM, CVAE, BaseAugmentationMethod
)
from .traditional_augmentation import TraditionalAugmentation
from .cddpm import CDDPM

logger = logging.getLogger(__name__)


class CDDPMWrapper:
    """CDDPM训练器包装器"""

    def __init__(self, config: Dict, device: torch.device):
        """
        初始化CDDPM包装器

        Args:
            config: 配置字典
            device: 计算设备
        """
        self.config = config
        self.device = device
        self.model = CDDPM(config).to(device)
        self.optimizer = None
        self.scheduler = None
        self.best_loss = float('inf')

        # 训练配置
        self.training_config = config['training']['diffusion']
        self.epochs = self.training_config['epochs']
        self.learning_rate = self.training_config['learning_rate']

        # 获取最佳模型判断配置
        best_model_config = self.training_config.get('best_model_criteria', {})
        self.best_metric = best_model_config.get('metric', 'val_loss')
        self.best_mode = best_model_config.get('mode', 'min')

        # 加权损失配置
        weighted_config = best_model_config.get('weighted_loss', {})
        self.train_weight = weighted_config.get('train_weight', 0.7)
        self.val_weight = weighted_config.get('val_weight', 0.3)

        # 初始化优化器
        self._setup_optimizer()

    def _setup_optimizer(self):
        """设置优化器和学习率调度器"""
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=self.training_config.get('weight_decay', 0.0001)
        )

        # 设置学习率调度器
        scheduler_config = self.training_config.get('scheduler', {})
        scheduler_type = scheduler_config.get('type', 'cosine')

        if scheduler_type == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=scheduler_config.get('T_max', self.epochs),
                eta_min=scheduler_config.get('eta_min', 1e-5)
            )
        elif scheduler_type == 'step':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=scheduler_config.get('step_size', 20),
                gamma=scheduler_config.get('gamma', 0.5)
            )
        else:
            self.scheduler = None

    def train(self, train_loader, val_loader=None) -> Dict:
        """
        训练CDDPM模型

        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器

        Returns:
            训练结果字典
        """
        logger.info(f"开始训练CDDPM模型，共{self.epochs}轮")

        train_losses = []
        val_losses = []

        self.model.train()

        for epoch in range(self.epochs):
            # 训练阶段
            epoch_train_loss = 0.0
            num_batches = 0

            # 不显示进度条，直接处理数据
            for batch_idx, (data, labels) in enumerate(train_loader):
                data = data.to(self.device)
                labels = labels.to(self.device)

                # 确保数据形状正确 [batch_size, 1, signal_length]
                if data.dim() == 2:
                    data = data.unsqueeze(1)

                self.optimizer.zero_grad()

                # 前向传播
                loss_dict = self.model(data, labels)
                loss = loss_dict['loss']

                # 反向传播
                loss.backward()
                self.optimizer.step()

                epoch_train_loss += loss.item()
                num_batches += 1

            avg_train_loss = epoch_train_loss / num_batches
            train_losses.append(avg_train_loss)

            # 验证阶段
            avg_val_loss = 0.0
            if val_loader is not None:
                avg_val_loss = self._validate(val_loader)
                val_losses.append(avg_val_loss)

            # 学习率调度
            if self.scheduler is not None:
                self.scheduler.step()

            # 保存最佳模型 - 根据配置的判断标准
            if self.best_metric == 'train_loss':
                current_score = avg_train_loss
            elif self.best_metric == 'val_loss':
                current_score = avg_val_loss if val_loader is not None else avg_train_loss
            elif self.best_metric == 'weighted_loss':
                if val_loader is not None:
                    current_score = self.train_weight * avg_train_loss + self.val_weight * avg_val_loss
                else:
                    current_score = avg_train_loss
            else:
                current_score = avg_val_loss if val_loader is not None else avg_train_loss

            is_best = False
            if current_score < self.best_loss:
                self.best_loss = current_score
                self._save_best_model()
                is_best = True

            # 每50个epoch或最佳时显示日志
            if (epoch + 1) % 50 == 0 or is_best or epoch == 0:
                status = " (Best✓)" if is_best else ""

                if val_loader is not None:
                    # 构建损失显示字符串
                    loss_info = f"Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}"

                    # 如果使用加权损失，也显示加权损失
                    if self.best_metric == 'weighted_loss':
                        weighted_loss = self.train_weight * avg_train_loss + self.val_weight * avg_val_loss
                        loss_info += f", Weighted Loss: {weighted_loss:.6f}"

                    logger.info(f"Epoch {epoch+1:3d}/{self.epochs}: {loss_info}{status}")
                else:
                    logger.info(f"Epoch {epoch+1:3d}/{self.epochs}: "
                              f"Train Loss: {avg_train_loss:.6f}{status}")

        logger.info("CDDPM训练完成")

        return {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'best_loss': self.best_loss,
            'total_epochs': self.epochs,
            'training_time': 0.0  # TODO: 添加计时
        }

    def _validate(self, val_loader) -> float:
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0

        with torch.no_grad():
            for data, labels in val_loader:
                data = data.to(self.device)
                labels = labels.to(self.device)

                if data.dim() == 2:
                    data = data.unsqueeze(1)

                loss_dict = self.model(data, labels)
                total_loss += loss_dict['loss'].item()
                num_batches += 1

        self.model.train()
        return total_loss / num_batches

    def _save_best_model(self):
        """保存最佳模型"""
        # 这里可以添加保存逻辑，暂时跳过
        pass

    def generate_samples(self, num_generated_per_class: int, signal_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成样本

        Args:
            num_generated_per_class: 每个类别生成的样本数
            signal_length: 信号长度

        Returns:
            生成的数据和标签
        """
        self.model.eval()

        # 获取类别数量
        num_classes = self.config['dataset']['datasets'][self.config['dataset']['name']]['num_classes']

        all_generated_data = []
        all_generated_labels = []

        with torch.no_grad():
            for class_id in range(num_classes):
                logger.info(f"生成类别 {class_id} 的样本...")

                # 创建类别标签
                class_labels = torch.full((num_generated_per_class,), class_id,
                                        dtype=torch.long, device=self.device)

                # 生成样本
                generated_data = self.model.sample(
                    num_generated_per_class, signal_length, class_labels
                )

                # 转换为numpy并移除通道维度
                generated_data = generated_data.squeeze(1).cpu().numpy()
                generated_labels = class_labels.cpu().numpy()

                all_generated_data.append(generated_data)
                all_generated_labels.append(generated_labels)

        # 合并所有数据
        all_generated_data = np.concatenate(all_generated_data, axis=0)
        all_generated_labels = np.concatenate(all_generated_labels, axis=0)

        logger.info(f"CDDPM生成完成: {len(all_generated_data)} 个样本")

        return all_generated_data, all_generated_labels

    def save_model(self, save_path: str):
        """保存模型"""
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'best_loss': self.best_loss,
            'config': self.config
        }, save_path)
        logger.info(f"CDDPM模型已保存: {save_path}")

    def load_model(self, load_path: str):
        """加载模型"""
        checkpoint = torch.load(load_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.best_loss = checkpoint['best_loss']
        logger.info(f"CDDPM模型已加载: {load_path}")


class AugmentationFactory:
    """数据增强方法工厂类"""
    
    # 支持的方法列表
    SUPPORTED_METHODS = [
        'CDDPM',      # 条件去噪扩散概率模型
        'CGAN',       # 条件生成对抗网络
        'WGAN',       # Wasserstein GAN
        'WGAN-GP',    # WGAN with Gradient Penalty
        'DDPM',       # 原始DDPM（无条件）
        'DCGAN',      # Deep Convolutional GAN
        'CVAE',       # 条件变分自编码器
        'ADASYN',     # Adaptive Synthetic Sampling
        'SMOTEENN',   # SMOTE + Edited Nearest Neighbours
        'KMEANS_SMOTE', # K-means SMOTE
        'RWO_SAMPLING'  # Random Walk Oversampling
    ]

    # 深度学习方法（需要训练）
    DEEP_LEARNING_METHODS = ['CDDPM', 'CGAN', 'WGAN', 'WGAN-GP', 'DDPM', 'DCGAN', 'CVAE']
    
    # 传统方法（不需要训练）
    TRADITIONAL_METHODS = ['ADASYN', 'SMOTEENN', 'KMEANS_SMOTE', 'RWO_SAMPLING']
    
    @staticmethod
    def create_method(method_name: str, config: Dict, device: torch.device) -> Any:
        """
        创建数据增强方法实例

        Args:
            method_name: 方法名称
            config: 配置字典
            device: 计算设备

        Returns:
            数据增强方法实例
        """
        method_name = method_name.upper()

        # 处理WGAN_GP和WGAN-GP的兼容性
        if method_name == 'WGAN_GP':
            method_name = 'WGAN-GP'

        if method_name not in AugmentationFactory.SUPPORTED_METHODS:
            raise ValueError(f"不支持的数据增强方法: {method_name}")

        logger.info(f"创建数据增强方法: {method_name}")
        
        # 深度学习方法
        if method_name == 'CDDPM':
            # 为CDDPM创建训练器包装器
            return CDDPMWrapper(config, device)
        elif method_name == 'CGAN':
            return CGAN(config, device)
        elif method_name == 'WGAN':
            return WGAN(config, device)
        elif method_name == 'WGAN-GP':
            return WGAN_GP(config, device)
        elif method_name == 'DDPM':
            return DDPM(config, device)
        elif method_name == 'DCGAN':
            return DCGAN(config, device)
        elif method_name == 'CVAE':
            return CVAE(config, device)
        
        # 传统方法
        elif method_name in AugmentationFactory.TRADITIONAL_METHODS:
            return TraditionalAugmentation(config)
        
        else:
            raise ValueError(f"未知的数据增强方法: {method_name}")
    
    @staticmethod
    def is_deep_learning_method(method_name: str) -> bool:
        """
        判断是否为深度学习方法

        Args:
            method_name: 方法名称

        Returns:
            是否为深度学习方法
        """
        method_name = method_name.upper()
        # 处理WGAN_GP和WGAN-GP的兼容性
        if method_name == 'WGAN_GP':
            method_name = 'WGAN-GP'
        return method_name in AugmentationFactory.DEEP_LEARNING_METHODS
    
    @staticmethod
    def is_traditional_method(method_name: str) -> bool:
        """
        判断是否为传统方法

        Args:
            method_name: 方法名称

        Returns:
            是否为传统方法
        """
        method_name = method_name.upper()
        # 处理WGAN_GP和WGAN-GP的兼容性
        if method_name == 'WGAN_GP':
            method_name = 'WGAN-GP'
        return method_name in AugmentationFactory.TRADITIONAL_METHODS
    
    @staticmethod
    def get_supported_methods() -> list:
        """
        获取支持的方法列表
        
        Returns:
            支持的方法列表
        """
        return AugmentationFactory.SUPPORTED_METHODS.copy()


class UnifiedAugmentationInterface:
    """统一的数据增强接口"""
    
    def __init__(self, method_name: str, config: Dict, device: torch.device):
        """
        初始化统一接口
        
        Args:
            method_name: 方法名称
            config: 配置字典
            device: 计算设备
        """
        self.method_name = method_name.upper()
        self.config = config
        self.device = device
        self.method = AugmentationFactory.create_method(method_name, config, device)
        self.is_deep_learning = AugmentationFactory.is_deep_learning_method(method_name)
        self.is_traditional = AugmentationFactory.is_traditional_method(method_name)
        
    def train(self, train_loader, val_loader=None) -> Dict:
        """
        训练模型（仅适用于深度学习方法）
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            
        Returns:
            训练结果字典
        """
        if self.is_deep_learning:
            logger.info(f"开始训练 {self.method_name} 模型...")
            return self.method.train(train_loader, val_loader)
        else:
            logger.info(f"{self.method_name} 是传统方法，无需训练")
            return self.method.get_training_results()
    
    def generate_samples(self, train_data: np.ndarray, train_labels: np.ndarray, 
                        num_generated_per_class: int, signal_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成样本
        
        Args:
            train_data: 训练数据（用于传统方法）
            train_labels: 训练标签（用于传统方法）
            num_generated_per_class: 每个类别生成的样本数
            signal_length: 信号长度
            
        Returns:
            生成的数据和标签
        """
        logger.info(f"使用 {self.method_name} 生成样本...")
        
        if self.is_deep_learning:
            # 深度学习方法
            return self.method.generate_samples(num_generated_per_class, signal_length)
        else:
            # 传统方法
            return self.method.generate_samples(
                self.method_name, train_data, train_labels, num_generated_per_class
            )
    
    def generate_fault_only_samples(self, train_data: np.ndarray, train_labels: np.ndarray, 
                                   num_generated_per_class: int, signal_length: int, 
                                   healthy_label: int = 0) -> Tuple[np.ndarray, np.ndarray]:
        """
        只生成故障样本（跳过健康样本）
        
        Args:
            train_data: 训练数据
            train_labels: 训练标签
            num_generated_per_class: 每个类别生成的样本数
            signal_length: 信号长度
            healthy_label: 健康样本标签
            
        Returns:
            生成的故障样本数据和标签
        """
        logger.info(f"使用 {self.method_name} 只生成故障样本...")
        
        if self.is_deep_learning:
            # 深度学习方法：生成所有类别然后过滤
            all_data, all_labels = self.method.generate_samples(num_generated_per_class, signal_length)
            
            # 过滤掉健康样本
            fault_mask = all_labels != healthy_label
            fault_data = all_data[fault_mask]
            fault_labels = all_labels[fault_mask]
            
            logger.info(f"过滤后的故障样本数量: {len(fault_data)}")
            return fault_data, fault_labels
            
        else:
            # 传统方法：只对故障样本进行增强
            fault_mask = train_labels != healthy_label
            fault_data = train_data[fault_mask]
            fault_labels = train_labels[fault_mask]
            
            if len(fault_data) == 0:
                logger.warning("没有故障样本可用于增强")
                return np.array([]), np.array([])
            
            return self.method.generate_samples(
                self.method_name, fault_data, fault_labels, num_generated_per_class
            )
    
    def save_model(self, save_path: str):
        """
        保存模型（仅适用于深度学习方法）
        
        Args:
            save_path: 保存路径
        """
        if self.is_deep_learning and hasattr(self.method, 'save_model'):
            self.method.save_model(save_path)
            logger.info(f"{self.method_name} 模型已保存: {save_path}")
        else:
            logger.info(f"{self.method_name} 无需保存模型")
    
    def load_model(self, load_path: str):
        """
        加载模型（仅适用于深度学习方法）
        
        Args:
            load_path: 加载路径
        """
        if self.is_deep_learning and hasattr(self.method, 'load_model'):
            self.method.load_model(load_path)
            logger.info(f"{self.method_name} 模型已加载: {load_path}")
        else:
            logger.info(f"{self.method_name} 无需加载模型")
    
    def get_method_info(self) -> Dict:
        """
        获取方法信息
        
        Returns:
            方法信息字典
        """
        return {
            'method_name': self.method_name,
            'is_deep_learning': self.is_deep_learning,
            'is_traditional': self.is_traditional,
            'requires_training': self.is_deep_learning,
            'supports_conditional': self.is_deep_learning,
            'config': self.config.get('augmentation', {}).get(self.method_name.lower(), {})
        }
