"""
实验管理器模块
支持多参数对比实验的自动配置和管理
"""

import os
import yaml
import copy
import itertools
from datetime import datetime
from typing import Dict, List, Any, Tuple, Union
import logging
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)


class ExperimentManager:
    """实验管理器，处理多参数对比实验"""
    
    def __init__(self, base_config: Dict):
        """
        初始化实验管理器
        
        Args:
            base_config: 基础配置字典
        """
        self.base_config = base_config
        self.experiment_configs = []
        self.results_summary = []
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def detect_comparison_parameters(self) -> Dict[str, List]:
        """
        自动检测配置中的多参数设置
        
        Returns:
            包含多参数的字典
        """
        comparison_params = {}
        
        # 检查数据集名称
        dataset_name = self.base_config['dataset']['name']
        if isinstance(dataset_name, list) and len(dataset_name) > 1:
            comparison_params['dataset.name'] = dataset_name
            
        # 检查数据加载参数
        data_loading = self.base_config['dataset']['data_loading']

        # 检查故障样本数量
        if 'fault_samples' in data_loading:
            fault_samples = data_loading['fault_samples'].get('max_fault_samples_per_class')
            if isinstance(fault_samples, list) and len(fault_samples) > 1:
                comparison_params['dataset.data_loading.fault_samples.max_fault_samples_per_class'] = fault_samples

        # 向后兼容：检查旧的max_samples_per_class参数
        max_samples = data_loading.get('max_samples_per_class')
        if isinstance(max_samples, list) and len(max_samples) > 1:
            comparison_params['dataset.data_loading.max_samples_per_class'] = max_samples

        # 检查健康样本数量
        if 'healthy_samples' in data_loading:
            healthy_samples = data_loading['healthy_samples'].get('max_healthy_samples')
            if isinstance(healthy_samples, list) and len(healthy_samples) > 1:
                comparison_params['dataset.data_loading.healthy_samples.max_healthy_samples'] = healthy_samples
        
        # 检查数据增强方法
        aug_method = self.base_config['augmentation']['method']
        if isinstance(aug_method, list) and len(aug_method) > 1:
            comparison_params['augmentation.method'] = aug_method
            
        # 检查生成样本数量
        num_generated = self.base_config['augmentation']['num_generated_per_class']
        if isinstance(num_generated, list) and len(num_generated) > 1:
            comparison_params['augmentation.num_generated_per_class'] = num_generated
            
        return comparison_params
    
    def generate_experiment_configs(self) -> List[Dict]:
        """
        生成所有实验配置的组合

        Returns:
            实验配置列表
        """
        comparison_params = self.detect_comparison_parameters()

        if not comparison_params:
            # 没有多参数，返回原始配置
            logger.info("未检测到多参数配置，使用单一实验模式")
            return [self.base_config]

        logger.info(f"检测到多参数配置: {list(comparison_params.keys())}")

        # 生成参数组合
        param_names = list(comparison_params.keys())
        param_values = list(comparison_params.values())

        # 计算所有组合
        combinations = list(itertools.product(*param_values))
        logger.info(f"总共需要运行 {len(combinations)} 个实验")

        configs = []
        for i, combination in enumerate(combinations):
            config = copy.deepcopy(self.base_config)

            # 应用参数组合
            for param_name, param_value in zip(param_names, combination):
                self._set_nested_value(config, param_name, param_value)

            # 同步健康样本数量与故障样本数量
            self._sync_healthy_samples_with_fault_samples(config)

            # 将非对比参数从列表转换为单一值
            self._convert_non_comparison_params(config, comparison_params)

            # 添加实验标识
            config['experiment']['current_experiment'] = {
                'index': i + 1,
                'total': len(combinations),
                'parameters': dict(zip(param_names, combination)),
                'timestamp': self.timestamp
            }

            configs.append(config)

        self.experiment_configs = configs
        return configs
    
    def _set_nested_value(self, config: Dict, key_path: str, value: Any):
        """
        设置嵌套字典的值
        
        Args:
            config: 配置字典
            key_path: 键路径，如 'dataset.data_loading.max_samples_per_class'
            value: 要设置的值
        """
        keys = key_path.split('.')
        current = config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value

    def _sync_healthy_samples_with_fault_samples(self, config: Dict):
        """
        同步健康样本数量与故障样本数量

        Args:
            config: 配置字典
        """
        try:
            data_loading = config.get('dataset', {}).get('data_loading', {})

            # 获取故障样本数量
            fault_samples = data_loading.get('fault_samples', {})
            max_fault_samples = fault_samples.get('max_fault_samples_per_class')

            # 获取健康样本配置
            healthy_samples = data_loading.get('healthy_samples', {})

            # 如果故障样本数量是数字且健康样本配置存在，则同步
            if isinstance(max_fault_samples, (int, float)) and healthy_samples:
                # 将健康样本数量设置为与故障样本数量相同
                healthy_samples['max_healthy_samples'] = max_fault_samples
                logger.debug(f"同步健康样本数量为: {max_fault_samples}")

        except Exception as e:
            logger.debug(f"同步健康样本数量时出错: {e}")

    def _convert_non_comparison_params(self, config: Dict, comparison_params: Dict):
        """
        将非对比参数从列表转换为单一值

        Args:
            config: 配置字典
            comparison_params: 对比参数字典
        """
        # 需要检查的所有可能的列表参数
        list_params = [
            'dataset.name',
            'dataset.data_loading.fault_samples.max_fault_samples_per_class',
            'dataset.data_loading.max_samples_per_class',  # 向后兼容
            'dataset.data_loading.healthy_samples.max_healthy_samples',
            'augmentation.method',
            'augmentation.num_generated_per_class'
        ]

        for param_path in list_params:
            # 如果这个参数不是对比参数，但在配置中是列表，则取第一个值
            if param_path not in comparison_params:
                try:
                    keys = param_path.split('.')
                    current = config

                    # 导航到参数位置
                    for key in keys[:-1]:
                        if key in current:
                            current = current[key]
                        else:
                            break
                    else:
                        # 检查最后一个键
                        final_key = keys[-1]
                        if final_key in current and isinstance(current[final_key], list):
                            # 如果是列表，取第一个值
                            if len(current[final_key]) > 0:
                                current[final_key] = current[final_key][0]
                                logger.debug(f"转换参数 {param_path} 从列表到单一值: {current[final_key]}")
                except Exception as e:
                    logger.debug(f"处理参数 {param_path} 时出错: {e}")
                    continue

    def save_experiment_config(self, config: Dict, experiment_index: int) -> str:
        """
        保存单个实验的配置文件
        
        Args:
            config: 实验配置
            experiment_index: 实验索引
            
        Returns:
            配置文件路径
        """
        # 创建临时配置目录
        temp_config_dir = os.path.join("temp_configs", self.timestamp)
        os.makedirs(temp_config_dir, exist_ok=True)
        
        # 生成配置文件名
        config_filename = f"experiment_{experiment_index:03d}_config.yaml"
        config_path = os.path.join(temp_config_dir, config_filename)
        
        # 保存配置文件，保持原始格式和注释
        self._save_yaml_with_comments(config, config_path)
        
        return config_path
    
    def _save_yaml_with_comments(self, config: Dict, filepath: str):
        """
        保存YAML文件并尽量保持原始格式和注释
        
        Args:
            config: 配置字典
            filepath: 文件路径
        """
        # 读取原始配置文件的内容和注释
        original_lines = []
        try:
            with open(self.base_config.get('_config_path', 'config.yaml'), 'r', encoding='utf-8') as f:
                original_lines = f.readlines()
        except:
            pass
        
        # 保存新配置
        with open(filepath, 'w', encoding='utf-8') as f:
            # 添加实验信息注释
            if 'current_experiment' in config.get('experiment', {}):
                exp_info = config['experiment']['current_experiment']
                f.write(f"# 自动生成的实验配置文件\n")
                f.write(f"# 实验 {exp_info['index']}/{exp_info['total']}\n")
                f.write(f"# 时间戳: {exp_info['timestamp']}\n")
                f.write(f"# 参数: {exp_info['parameters']}\n")
                f.write(f"\n")
            
            # 保存配置
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    def create_results_directory(self, dataset_name: str) -> str:
        """
        创建结果目录
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            结果目录路径
        """
        base_results_dir = self.base_config['system']['save']['results_dir']
        
        # 创建数据集特定的目录
        dataset_results_dir = os.path.join(base_results_dir, dataset_name)
        
        # 创建时间戳目录
        timestamp_dir = os.path.join(dataset_results_dir, self.timestamp)
        os.makedirs(timestamp_dir, exist_ok=True)
        
        return timestamp_dir
    
    def add_experiment_result(self, config: Dict, results: Dict):
        """
        添加实验结果
        
        Args:
            config: 实验配置
            results: 实验结果
        """
        if 'current_experiment' not in config.get('experiment', {}):
            return
            
        exp_info = config['experiment']['current_experiment']
        
        # 提取关键指标
        result_summary = {
            'experiment_index': exp_info['index'],
            'timestamp': exp_info['timestamp'],
            'dataset': config['dataset']['name'],
            'augmentation_method': config['augmentation']['method'],
            'num_generated_per_class': config['augmentation']['num_generated_per_class'],
        }

        # 添加故障样本信息
        if 'fault_samples' in config['dataset']['data_loading']:
            result_summary['max_fault_samples_per_class'] = config['dataset']['data_loading']['fault_samples']['max_fault_samples_per_class']

        # 向后兼容
        if 'max_samples_per_class' in config['dataset']['data_loading']:
            result_summary['max_samples_per_class'] = config['dataset']['data_loading']['max_samples_per_class']

        # 添加健康样本信息
        if 'healthy_samples' in config['dataset']['data_loading']:
            max_healthy = config['dataset']['data_loading']['healthy_samples']['max_healthy_samples']
            result_summary['max_healthy_samples'] = max_healthy
            result_summary['use_healthy'] = max_healthy > 0  # 自动根据数量确定
        
        # 添加性能指标
        if 'performance_comparison' in results:
            perf = results['performance_comparison']
            result_summary.update({
                'baseline_accuracy': perf['baseline_metrics']['accuracy'],
                'baseline_precision': perf['baseline_metrics']['precision'],
                'baseline_recall': perf['baseline_metrics']['recall'],
                'baseline_f1_score': perf['baseline_metrics']['f1_score'],
                'augmented_accuracy': perf['augmented_metrics']['accuracy'],
                'augmented_precision': perf['augmented_metrics']['precision'],
                'augmented_recall': perf['augmented_metrics']['recall'],
                'augmented_f1_score': perf['augmented_metrics']['f1_score'],
                'accuracy_improvement': perf['improvement']['accuracy'],
                'precision_improvement': perf['improvement']['precision'],
                'recall_improvement': perf['improvement']['recall'],
                'f1_improvement': perf['improvement']['f1_score'],
            })
        
        # 添加训练时间
        if 'augmentation_results' in results:
            result_summary['augmentation_training_time'] = results['augmentation_results'].get('training_time', 0.0)
        if 'classifier_results' in results:
            result_summary['classifier_training_time'] = results['classifier_results']['training_time']
        
        # 添加GAN指标
        if 'gan_results' in results and results['gan_results']:
            if 'gan_train' in results['gan_results']:
                result_summary['gan_train_score'] = results['gan_results']['gan_train']
            if 'gan_test' in results['gan_results']:
                result_summary['gan_test_score'] = results['gan_results']['gan_test']
        
        self.results_summary.append(result_summary)
    
    def save_comparison_results(self, results_dir: str):
        """
        保存对比实验结果汇总
        
        Args:
            results_dir: 结果目录
        """
        if not self.results_summary:
            return
        
        # 转换为DataFrame
        df = pd.DataFrame(self.results_summary)
        
        # 保存CSV文件
        csv_path = os.path.join(results_dir, "comparison_results_summary.csv")
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        logger.info(f"对比实验结果汇总已保存: {csv_path}")
        
        # 打印汇总信息
        self._print_comparison_summary(df)
        
        return csv_path
    
    def _print_comparison_summary(self, df: pd.DataFrame):
        """
        打印对比实验汇总信息
        
        Args:
            df: 结果DataFrame
        """
        logger.info("=" * 80)
        logger.info("对比实验结果汇总")
        logger.info("=" * 80)
        
        # 按数据集分组显示
        if 'dataset' in df.columns:
            for dataset in df['dataset'].unique():
                dataset_df = df[df['dataset'] == dataset]
                logger.info(f"\n数据集: {dataset}")
                logger.info("-" * 40)
                
                for _, row in dataset_df.iterrows():
                    # 安全获取值并格式化
                    method = row.get('augmentation_method', 'N/A')
                    samples = row.get('max_samples_per_class', 'N/A')
                    generated = row.get('num_generated_per_class', 'N/A')

                    # 格式化数值
                    samples_str = f"{samples:3d}" if isinstance(samples, (int, float)) else f"{samples:>3s}"
                    generated_str = f"{generated:3d}" if isinstance(generated, (int, float)) else f"{generated:>3s}"

                    logger.info(f"实验 {row['experiment_index']:2d}: "
                              f"方法={method:8s} | "
                              f"样本={samples_str} | "
                              f"生成={generated_str} | "
                              f"准确率={row.get('augmented_accuracy', 0):.4f} | "
                              f"提升={row.get('accuracy_improvement', 0):+.4f}")
        
        # 显示最佳结果
        if 'augmented_accuracy' in df.columns:
            best_idx = df['augmented_accuracy'].idxmax()
            best_result = df.loc[best_idx]
            
            logger.info(f"\n最佳结果:")
            logger.info(f"  实验 {best_result['experiment_index']}: "
                       f"准确率 {best_result['augmented_accuracy']:.4f}")
            logger.info(f"  参数: 方法={best_result.get('augmentation_method', 'N/A')}, "
                       f"样本={best_result.get('max_samples_per_class', 'N/A')}, "
                       f"生成={best_result.get('num_generated_per_class', 'N/A')}")
    
    def cleanup_temp_configs(self):
        """清理临时配置文件（已禁用，保留缓存文件）"""
        # 不再清理缓存文件，方便后续检查对照
        logger.info("缓存配置文件已保留，不进行清理")

    def group_experiments_by_training_data(self) -> Dict[str, List[Dict]]:
        """
        根据训练数据配置对实验进行分组
        相同训练数据配置的实验可以重用扩散模型

        Returns:
            分组后的实验配置字典
        """
        if not self.experiment_configs:
            self.generate_experiment_configs()

        groups = {}

        for config in self.experiment_configs:
            # 生成训练数据的唯一标识
            data_key = self._generate_training_data_key(config)

            if data_key not in groups:
                groups[data_key] = []

            groups[data_key].append(config)

        logger.info(f"实验分组完成，共 {len(groups)} 个训练数据组")
        for i, (key, configs) in enumerate(groups.items()):
            logger.info(f"  组 {i+1}: {len(configs)} 个实验 - {key}")

        return groups

    def _generate_training_data_key(self, config: Dict) -> str:
        """
        生成训练数据配置的唯一标识

        Args:
            config: 实验配置

        Returns:
            训练数据的唯一标识字符串
        """
        key_parts = []

        # 数据集名称
        dataset_name = config['dataset']['name']
        key_parts.append(f"dataset={dataset_name}")

        # 原始样本数量（影响扩散模型训练）
        data_loading = config['dataset']['data_loading']

        # 故障样本数量
        if 'fault_samples' in data_loading:
            fault_samples = data_loading['fault_samples'].get('max_fault_samples_per_class', 'all')
            key_parts.append(f"fault={fault_samples}")

        # 向后兼容：检查旧的max_samples_per_class参数
        if 'max_samples_per_class' in data_loading:
            max_samples = data_loading.get('max_samples_per_class', 'all')
            key_parts.append(f"samples={max_samples}")

        # 健康样本数量
        if 'healthy_samples' in data_loading:
            healthy_samples = data_loading['healthy_samples'].get('max_healthy_samples', 0)
            key_parts.append(f"healthy={healthy_samples}")

        # 数据预处理参数（影响扩散模型训练）
        signal_length = data_loading.get('signal_length', 'default')
        key_parts.append(f"length={signal_length}")

        return "_".join(key_parts)

    def should_reuse_diffusion_model(self, comparison_params: Dict) -> bool:
        """
        判断是否应该重用扩散模型

        Args:
            comparison_params: 对比参数字典

        Returns:
            是否应该重用扩散模型
        """
        # 如果只是生成样本数量不同，可以重用扩散模型
        generation_only_params = {
            'augmentation.num_generated_per_class'
        }

        # 如果只是筛选参数不同，也可以重用扩散模型
        screening_params = {
            'data_screening.confidence_filter.threshold',
            'data_screening.influence_filter.ratio',
            'data_screening.diversity_selection.method'
        }

        # 检查是否只有生成或筛选参数在变化
        param_keys = set(comparison_params.keys())

        # 如果只有生成数量参数在变化
        if param_keys.issubset(generation_only_params):
            logger.info("检测到只有生成数量参数变化，将重用扩散模型")
            return True

        # 如果只有筛选参数在变化
        if param_keys.issubset(screening_params):
            logger.info("检测到只有筛选参数变化，将重用扩散模型")
            return True

        # 如果同时有生成和筛选参数变化
        if param_keys.issubset(generation_only_params.union(screening_params)):
            logger.info("检测到生成和筛选参数变化，将重用扩散模型")
            return True

        # 如果训练数据参数没有变化，也可以重用
        training_data_params = {
            'dataset.name',
            'dataset.data_loading.fault_samples.max_fault_samples_per_class',
            'dataset.data_loading.max_samples_per_class',
            'dataset.data_loading.healthy_samples.max_healthy_samples'
        }

        if not param_keys.intersection(training_data_params):
            logger.info("检测到训练数据参数未变化，将重用扩散模型")
            return True

        logger.info("检测到训练数据参数变化，需要重新训练扩散模型")
        return False
