% SK数据集预处理程序 - 顺序采样版本
% 功能说明：
% 1. 处理SK轴承故障数据集，采用顺序采样方式而非随机采样
% 2. 支持设置重叠长度，在每个4096点样本内按顺序提取1024点子样本
% 3. 训练集从前50%样本选择，测试集从后50%样本选择，确保独立性
% 4. 三种状态：正常(1-40组)、故障预警(81-120组)、故障(211-250组)
% 5. 可配置样本长度、重叠长度等参数
% 6. 数据保存为MATLAB和NPY格式，便于后续使用
% 
% 原样本包括三种状态：正常(1-40组)、故障预警(81-120组)、故障(211-250组)
% 每个原始样本长度为4096点，可分割成多个1024点子样本

% 清空工作区和命令窗口
clear;
clc;

try
    % 开始计时
    tic;
    
    % 调用处理函数
    process_SK_dataset_sequential();
    
    % 结束计时
    elapsed_time = toc;
    fprintf('处理完成! 耗时: %.2f 秒\n', elapsed_time);
catch e
    % 捕获并显示错误
    fprintf('处理过程中发生错误:\n');
    disp(e.message);
    if ~isempty(e.stack)
        fprintf('错误位置: %s (行 %d)\n', e.stack(1).name, e.stack(1).line);
    end
end

% ================== 主处理函数 ==================
function process_SK_dataset_sequential()
    % ==================== 参数设置 ====================
    sample_length = 1024;         % 每个子样本的长度（采样点数）
    overlap_length = 0;           % 重叠长度（采样点数），默认不重叠
    original_length = 4096;       % 原始样本长度
    train_ratio = 0.5;            % 训练集占每个类别的比例（前50%）
    random_seed = 42;             % 随机种子，确保结果可重现（用于最终打乱）

    % 样本数量控制参数
    max_samples_per_signal = 0;   % 每个原始信号最多提取的子样本数（0表示提取所有可能的样本）
    train_samples_per_class = 0;  % 每个类别最多生成的训练样本数（0表示不限制）
    test_samples_per_class = 0;   % 每个类别最多生成的测试样本数（0表示不限制）
    
    % 样本索引设置
    normal_indices = 1:40;        % 正常样本索引范围
    warning_indices = 81:120;     % 故障预警样本索引范围
    fault_indices = 211:250;      % 故障样本索引范围
    
    % ==================== 路径设置 ====================
    % 获取脚本所在目录的上级目录作为基础路径
    script_dir = fileparts(mfilename('fullpath'));
    base_dir = fileparts(script_dir);  % 上级目录
    
    % 使用基础路径构建完整路径
    ori_path = fullfile(base_dir, 'dataset', 'SK', 'ori_mat');      % 原始数据目录
    final_mat_path = fullfile(base_dir, 'dataset', 'SK', 'used_mat_sequential');  % 最终mat文件保存目录
    final_npy_path = fullfile(base_dir, 'dataset', 'SK', 'used_npy_sequential');  % 最终npy文件保存目录
    
    fprintf('使用以下路径:\n');
    fprintf('原始数据目录: %s\n', ori_path);
    fprintf('Mat文件保存目录: %s\n', final_mat_path);
    fprintf('NPY文件保存目录: %s\n', final_npy_path);
    fprintf('参数设置:\n');
    fprintf('- 原始样本长度: %d 个采样点\n', original_length);
    fprintf('- 子样本长度: %d 个采样点\n', sample_length);
    fprintf('- 重叠长度: %d 个采样点\n', overlap_length);
    fprintf('- 步长: %d 个采样点\n', sample_length - overlap_length);
    fprintf('- 训练集比例: %.0f%% (前半部分样本)\n', train_ratio*100);
    fprintf('- 测试集比例: %.0f%% (后半部分样本)\n', (1-train_ratio)*100);
    if max_samples_per_signal > 0
        fprintf('- 每个原始信号最多提取: %d 个子样本\n', max_samples_per_signal);
    else
        fprintf('- 每个原始信号提取: 所有可能的子样本\n');
    end
    if train_samples_per_class > 0
        fprintf('- 每个类别最多训练样本: %d 个\n', train_samples_per_class);
    else
        fprintf('- 每个类别训练样本: 不限制数量\n');
    end
    if test_samples_per_class > 0
        fprintf('- 每个类别最多测试样本: %d 个\n', test_samples_per_class);
    else
        fprintf('- 每个类别测试样本: 不限制数量\n');
    end
    
    % 检查原始数据目录是否存在
    if ~exist(ori_path, 'dir')
        error('原始数据目录不存在: %s', ori_path);
    end
    
    % 检查参数合理性
    if overlap_length >= sample_length
        error('重叠长度(%d)不能大于等于样本长度(%d)', overlap_length, sample_length);
    end
    
    if sample_length > original_length
        error('子样本长度(%d)不能大于原始样本长度(%d)', sample_length, original_length);
    end
    
    fprintf('开始处理SK数据集...\n');
    
    % ==================== 创建保存目录 ====================
    if ~exist(fullfile(final_mat_path, 'train'), 'dir')
        mkdir(fullfile(final_mat_path, 'train'));
    end
    if ~exist(fullfile(final_mat_path, 'test'), 'dir')
        mkdir(fullfile(final_mat_path, 'test'));
    end
    if ~exist(fullfile(final_npy_path, 'train'), 'dir')
        mkdir(fullfile(final_npy_path, 'train'));
    end
    if ~exist(fullfile(final_npy_path, 'test'), 'dir')
        mkdir(fullfile(final_npy_path, 'test'));
    end
    
    % ==================== 读取原始数据 ====================
    fprintf('读取原始数据文件...\n');
    
    % 找到原始mat文件
    mat_files = dir(fullfile(ori_path, '*.mat'));
    if isempty(mat_files)
        error('未在 %s 目录下找到mat文件', ori_path);
    end
    
    fprintf('找到以下mat文件:\n');
    for i = 1:length(mat_files)
        fprintf('  %s (%d bytes)\n', mat_files(i).name, mat_files(i).bytes);
    end
    
    % 读取第一个mat文件
    mat_file_path = fullfile(ori_path, mat_files(1).name);
    fprintf('读取文件: %s\n', mat_file_path);
    try
        original_data = load(mat_file_path);
    catch e
        fprintf('读取文件 %s 时出错:\n', mat_file_path);
        rethrow(e);
    end
    
    % 获取数据字段名
    field_names = fieldnames(original_data);
    if isempty(field_names)
        error('mat文件中未找到数据');
    end
    
    % 获取原始波形数据
    signals = original_data.(field_names{1});
    fprintf('成功读取%d组原始波形，每组长度%d\n', size(signals, 1), size(signals, 2));
    
    % ==================== 创建可视化窗口 ====================
    figure('Name', 'SK数据集样本示例（顺序采样）', 'Position', [100, 100, 1200, 800]);
    
    % ==================== 处理每个类别的数据 ====================
    all_train_data = [];
    all_train_labels = [];
    all_test_data = [];
    all_test_labels = [];
    
    % 处理正常样本 (标签0)
    fprintf('处理正常样本 (标签0)...\n');
    [train_samples, test_samples] = process_class_samples_sequential(...
        signals(normal_indices, :), 0, sample_length, overlap_length, train_ratio, ...
        max_samples_per_signal, train_samples_per_class, test_samples_per_class);
    
    all_train_data = [all_train_data; train_samples];
    all_train_labels = [all_train_labels; zeros(size(train_samples, 1), 1)];
    all_test_data = [all_test_data; test_samples];
    all_test_labels = [all_test_labels; zeros(size(test_samples, 1), 1)];
    
    % 可视化部分样本
    subplot(3, 1, 1);
    plot(train_samples(1, :));
    title('正常样本示例 (标签0) - 顺序采样');
    
    % 处理故障预警样本 (标签1)
    fprintf('处理故障预警样本 (标签1)...\n');
    [train_samples, test_samples] = process_class_samples_sequential(...
        signals(warning_indices, :), 1, sample_length, overlap_length, train_ratio, ...
        max_samples_per_signal, train_samples_per_class, test_samples_per_class);
    
    all_train_data = [all_train_data; train_samples];
    all_train_labels = [all_train_labels; ones(size(train_samples, 1), 1)];
    all_test_data = [all_test_data; test_samples];
    all_test_labels = [all_test_labels; ones(size(test_samples, 1), 1)];
    
    % 可视化部分样本
    subplot(3, 1, 2);
    plot(train_samples(1, :));
    title('故障预警样本示例 (标签1) - 顺序采样');
    
    % 处理故障样本 (标签2)
    fprintf('处理故障样本 (标签2)...\n');
    [train_samples, test_samples] = process_class_samples_sequential(...
        signals(fault_indices, :), 2, sample_length, overlap_length, train_ratio, ...
        max_samples_per_signal, train_samples_per_class, test_samples_per_class);
    
    all_train_data = [all_train_data; train_samples];
    all_train_labels = [all_train_labels; 2*ones(size(train_samples, 1), 1)];
    all_test_data = [all_test_data; test_samples];
    all_test_labels = [all_test_labels; 2*ones(size(test_samples, 1), 1)];
    
    % 可视化部分样本
    subplot(3, 1, 3);
    plot(train_samples(1, :));
    title('故障样本示例 (标签2) - 顺序采样');
    
    % 保存样本可视化图像
    saveas(gcf, fullfile(final_mat_path, 'samples_visualization_sequential.png'));
    
    fprintf('总共生成训练样本: %d 个\n', size(all_train_data, 1));
    fprintf('总共生成测试样本: %d 个\n', size(all_test_data, 1));
    
    % ==================== 随机打乱样本顺序 ====================
    fprintf('随机打乱样本顺序...\n');
    
    % 设置随机种子以确保结果可重现
    rng(random_seed);
    
    % 随机打乱训练集
    train_shuffle_idx = randperm(size(all_train_data, 1));
    all_train_data = all_train_data(train_shuffle_idx, :);
    all_train_labels = int32(all_train_labels(train_shuffle_idx));  % 转换为int32类型
    
    % 随机打乱测试集
    test_shuffle_idx = randperm(size(all_test_data, 1));
    all_test_data = all_test_data(test_shuffle_idx, :);
    all_test_labels = int32(all_test_labels(test_shuffle_idx));  % 转换为int32类型
    
    fprintf('最终训练集: %d 个样本\n', size(all_train_data, 1));
    fprintf('最终测试集: %d 个样本\n', size(all_test_data, 1));

    % ==================== 保存为MATLAB格式 ====================
    fprintf('保存为MATLAB格式...\n');

    % 保存训练集
    save(fullfile(final_mat_path, 'train', 'train_dataset_sequential.mat'), 'all_train_data', 'all_train_labels');

    % 保存测试集
    save(fullfile(final_mat_path, 'test', 'test_dataset_sequential.mat'), 'all_test_data', 'all_test_labels');

    % ==================== 生成并执行Python脚本来转换为NPY格式 ====================
    fprintf('生成并执行Python脚本来转换为NPY格式...\n');

    % 创建临时Python脚本文件
    temp_dir = fullfile(tempdir, 'sk_temp_sequential');
    if ~exist(temp_dir, 'dir')
        mkdir(temp_dir);
    end

    py_script_path = fullfile(temp_dir, 'convert_to_numpy_sequential.py');
    fid = fopen(py_script_path, 'w');

    if fid < 0
        error('无法创建Python转换脚本: %s', py_script_path);
    end

    % 写入Python脚本内容
    fprintf(fid, '#!/usr/bin/env python\n');
    fprintf(fid, '# -*- coding: utf-8 -*-\n\n');
    fprintf(fid, '# 此脚本由MATLAB自动生成，用于将SK数据集从MAT格式转换为NPY格式（顺序采样版本）\n\n');

    fprintf(fid, 'import numpy as np\n');
    fprintf(fid, 'import scipy.io as sio\n');
    fprintf(fid, 'import os\n\n');

    fprintf(fid, '# 创建输出目录\n');
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n', strrep(fullfile(final_npy_path, 'train'), '\', '\\'));
    fprintf(fid, 'os.makedirs("%s", exist_ok=True)\n\n', strrep(fullfile(final_npy_path, 'test'), '\', '\\'));

    fprintf(fid, '# 加载训练数据\n');
    fprintf(fid, 'print("加载训练数据...")\n');
    fprintf(fid, 'train_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'train', 'train_dataset_sequential.mat'), '\', '\\'));

    fprintf(fid, '# 加载测试数据\n');
    fprintf(fid, 'print("加载测试数据...")\n');
    fprintf(fid, 'test_data = sio.loadmat("%s")\n\n', strrep(fullfile(final_mat_path, 'test', 'test_dataset_sequential.mat'), '\', '\\'));

    fprintf(fid, '# 保存训练数据\n');
    fprintf(fid, 'print("保存训练数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", train_data["all_train_data"])\n', strrep(fullfile(final_npy_path, 'train', 'sk_data_sequential.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", train_data["all_train_labels"])\n\n', strrep(fullfile(final_npy_path, 'train', 'sk_label_sequential.npy'), '\', '\\'));

    fprintf(fid, '# 保存测试数据\n');
    fprintf(fid, 'print("保存测试数据为NPY格式...")\n');
    fprintf(fid, 'np.save("%s", test_data["all_test_data"])\n', strrep(fullfile(final_npy_path, 'test', 'sk_data_sequential.npy'), '\', '\\'));
    fprintf(fid, 'np.save("%s", test_data["all_test_labels"])\n\n', strrep(fullfile(final_npy_path, 'test', 'sk_label_sequential.npy'), '\', '\\'));

    fprintf(fid, 'print("转换完成！")\n');
    fclose(fid);

    % 创建说明文件
    readme_path = fullfile(final_npy_path, 'README_sequential.txt');
    fid = fopen(readme_path, 'w');
    if fid > 0
        fprintf(fid, 'SK数据集 - NPY格式数据说明（顺序采样版本）\n');
        fprintf(fid, '==========================================\n\n');
        fprintf(fid, '数据格式说明:\n');
        fprintf(fid, '- NPY文件格式: \n');
        fprintf(fid, '  * 训练数据: sk_data_sequential.npy (样本矩阵), sk_label_sequential.npy (整数类型标签)\n');
        fprintf(fid, '  * 测试数据: sk_data_sequential.npy (样本矩阵), sk_label_sequential.npy (整数类型标签)\n\n');
        fprintf(fid, '标签说明:\n');
        fprintf(fid, '- 0: 正常\n');
        fprintf(fid, '- 1: 故障预警\n');
        fprintf(fid, '- 2: 故障\n\n');
        fprintf(fid, '样本数量:\n');
        fprintf(fid, '- 训练集: %d 个样本\n', size(all_train_data, 1));
        fprintf(fid, '- 测试集: %d 个样本\n\n', size(all_test_data, 1));
        fprintf(fid, '采样方法:\n');
        fprintf(fid, '- 采用顺序采样方式，在每个4096点原始样本内按顺序提取1024点子样本\n');
        fprintf(fid, '- 训练样本从每个类别的前%.0f%%样本选取，测试样本从后%.0f%%样本选取\n', train_ratio*100, (1-train_ratio)*100);
        fprintf(fid, '- 每个子样本包含 %d 个数据点，相邻子样本重叠 %d 个数据点\n', sample_length, overlap_length);
        fprintf(fid, '- 原始样本长度: %d 个数据点\n', original_length);
        fclose(fid);
    end

    % 执行Python脚本
    fprintf('正在执行Python脚本以生成NPY格式数据文件...\n');

    try
        % 判断系统平台
        if ispc
            % Windows系统
            [status, cmdout] = system(['python "', py_script_path, '"']);
        else
            % Linux/Mac系统
            [status, cmdout] = system(['python3 "', py_script_path, '"']);
        end

        if status == 0
            fprintf('Python脚本执行成功！\n%s\n', cmdout);
        else
            fprintf('Python脚本执行失败！错误码: %d\n%s\n', status, cmdout);
            fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
            fprintf('python "%s"\n', py_script_path);
        end
    catch e
        fprintf('执行Python脚本时出错: %s\n', e.message);
        fprintf('请手动执行以下命令来生成NPY格式数据文件:\n');
        fprintf('python "%s"\n', py_script_path);
    end

    % 尝试删除临时Python脚本
    try
        delete(py_script_path);
        rmdir(temp_dir);
        fprintf('临时Python脚本已删除\n');
    catch
        fprintf('无法删除临时Python脚本: %s\n', py_script_path);
    end

    fprintf('SK数据集预处理完成（顺序采样版本）!\n');
end

function [train_samples, test_samples] = process_class_samples_sequential(class_signals, label, sample_length, overlap_length, train_ratio, max_samples_per_signal, train_samples_per_class, test_samples_per_class)
    % 将一个类别的所有原始信号处理成训练和测试样本（顺序采样）
    % 输入:
    %   class_signals: 原始信号，每行一个波形（4096点）
    %   label: 类别标签
    %   sample_length: 每个子样本的长度（1024点）
    %   overlap_length: 相邻子样本的重叠长度
    %   train_ratio: 训练集比例（前50%样本用于训练）
    %   max_samples_per_signal: 每个原始信号最多提取的子样本数（0表示不限制）
    %   train_samples_per_class: 每个类别最多生成的训练样本数（0表示不限制）
    %   test_samples_per_class: 每个类别最多生成的测试样本数（0表示不限制）
    % 输出:
    %   train_samples: 训练样本矩阵，每行一个样本
    %   test_samples: 测试样本矩阵，每行一个样本

    num_signals = size(class_signals, 1);

    % 计算训练和测试的样本分割点
    train_count = round(num_signals * train_ratio);

    % 分离训练和测试的原始信号
    train_signals = class_signals(1:train_count, :);
    test_signals = class_signals(train_count+1:end, :);

    fprintf('  类别 %d: 总共 %d 个原始样本，训练用 %d 个，测试用 %d 个\n', ...
        label, num_signals, size(train_signals, 1), size(test_signals, 1));

    % 处理训练样本
    train_samples = [];
    for i = 1:size(train_signals, 1)
        signal = train_signals(i, :);
        sub_samples = extract_sequential_samples_from_signal(signal, sample_length, overlap_length, max_samples_per_signal);
        train_samples = [train_samples; sub_samples];

        % 检查是否达到训练样本数量限制
        if train_samples_per_class > 0 && size(train_samples, 1) >= train_samples_per_class
            train_samples = train_samples(1:train_samples_per_class, :);
            fprintf('    训练样本已达到限制数量 %d，停止提取\n', train_samples_per_class);
            break;
        end
    end

    % 处理测试样本
    test_samples = [];
    for i = 1:size(test_signals, 1)
        signal = test_signals(i, :);
        sub_samples = extract_sequential_samples_from_signal(signal, sample_length, overlap_length, max_samples_per_signal);
        test_samples = [test_samples; sub_samples];

        % 检查是否达到测试样本数量限制
        if test_samples_per_class > 0 && size(test_samples, 1) >= test_samples_per_class
            test_samples = test_samples(1:test_samples_per_class, :);
            fprintf('    测试样本已达到限制数量 %d，停止提取\n', test_samples_per_class);
            break;
        end
    end

    fprintf('  类别 %d: 生成训练样本 %d 个，测试样本 %d 个\n', ...
        label, size(train_samples, 1), size(test_samples, 1));
end

function sub_samples = extract_sequential_samples_from_signal(signal, sample_length, overlap_length, max_samples)
    % 从单个4096点信号中按顺序提取1024点子样本
    % 输入:
    %   signal: 单个原始信号（4096点）
    %   sample_length: 子样本长度（1024点）
    %   overlap_length: 重叠长度
    %   max_samples: 最多提取的子样本数（0表示不限制）
    % 输出:
    %   sub_samples: 提取的子样本矩阵，每行一个子样本

    signal_length = length(signal);
    step_size = sample_length - overlap_length;  % 步长

    % 检查信号长度是否足够
    if signal_length < sample_length
        error('信号长度(%d)不足以提取长度为%d的样本', signal_length, sample_length);
    end

    % 检查步长是否合理
    if step_size <= 0
        error('重叠长度(%d)不能大于等于样本长度(%d)', overlap_length, sample_length);
    end

    % 计算可以提取的最大样本数
    max_possible_samples = floor((signal_length - sample_length) / step_size) + 1;

    % 确定实际提取的样本数
    if max_samples > 0 && max_samples < max_possible_samples
        num_samples = max_samples;
    else
        num_samples = max_possible_samples;
    end

    % 初始化样本矩阵
    sub_samples = zeros(num_samples, sample_length);

    % 按顺序提取样本
    for i = 1:num_samples
        start_idx = (i - 1) * step_size + 1;
        end_idx = start_idx + sample_length - 1;
        sub_samples(i, :) = signal(start_idx:end_idx);
    end
end
